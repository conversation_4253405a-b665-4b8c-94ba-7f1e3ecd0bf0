# 应用配置
APP_NAME="MDM Backend API"
APP_VERSION=1.0.0
DEBUG=true

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
# SQLite (开发环境)
# DATABASE_URL=sqlite:///./mdm_backend.db
# MySQL (生产环境)
DATABASE_URL=mysql+aiomysql://root:123456@localhost:3306/mdm_backend
# 是否自动初始化数据库表（首次启动或表不存在时）
AUTO_INIT_DB=true

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS="['.csv', '.xlsx', '.xls']"

# CORS配置
CORS_ORIGINS="['http://localhost:3000', 'http://localhost:8080', 'http://localhost:5173']"
