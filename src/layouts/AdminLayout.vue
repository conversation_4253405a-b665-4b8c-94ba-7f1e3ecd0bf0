<template>
  <div class="common-layout">
    <el-container class="admin-layout">
    <!-- 左侧菜单 -->
    <el-aside width="250px" class="sidebar">
      <div class="logo">
        <img src="/Roche_Logo_800px_Blue_RGB_Roche_Logo_RGB.png" alt="Roche" class="logo-img">
        <span class="logo-text">MDM系统</span>
      </div>
      
      <el-menu
        :default-active="$route.path"
        class="sidebar-menu"
        router
        background-color="#142f4f"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
<!--        <el-menu-item index="/">-->
<!--          <el-icon><House /></el-icon>-->
<!--          <span>仪表板</span>-->
<!--        </el-menu-item>-->
        
        <el-menu-item index="/data-loader">
          <el-icon><Upload /></el-icon>
          <span>数据加载器</span>
        </el-menu-item>
        
<!--        <el-menu-item index="/external-match">-->
<!--          <el-icon><Connection /></el-icon>-->
<!--          <span>外部匹配</span>-->
<!--        </el-menu-item>-->
        
        <el-menu-item index="/user-management">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </el-menu-item>
<!--        <el-menu-item index="/api-test">-->
<!--          <el-icon><Setting /></el-icon>-->
<!--          <span>API测试</span>-->
<!--        </el-menu-item>-->
      </el-menu>
    </el-aside>

    <el-container class="main-container">
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <h2>主数据管理系统</h2>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-dropdown">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.user?.username }}</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  House,
  Upload,
  Connection,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const userAvatar = ref('')

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        // 先清除本地存储，确保路由守卫不会阻止跳转
        localStorage.removeItem('token')
        localStorage.removeItem('user')

        // 调用store的logout方法
        await authStore.logout()

        // 跳转到登录页
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        // 确保即使出错也清除认证状态并跳转
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        router.push('/login')
      }
      break
  }
}
</script>

<style scoped>
.common-layout {
  height: 100vh;
}

.admin-layout {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: calc(100vw - 250px);
}

.sidebar {
  background-color: #142f4f;
  overflow: hidden;
  width: 250px !important;
  flex-shrink: 0;
}

.logo {
  display: flex;
  align-items: center;
  padding: 20px;
  color: white;
  border-bottom: 1px solid #434a50;
}

.logo-img {
  height: 32px;
  margin-right: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.sidebar-menu {
  border: none;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  width: 100%;
  flex-shrink: 0;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  color: #606266;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  width: 100%;
}
</style>
