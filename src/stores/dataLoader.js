import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { dataLoaderAPI } from '@/api'

export const useDataLoaderStore = defineStore('dataLoader', () => {
  const tasks = ref([])
  const loading = ref(false)
  const uploadProgress = ref(0)
  const validationResult = ref(null)
  const validating = ref(false)

  // 验证文件数据
  const validateFile = async (file, entityType) => {
    validating.value = true
    validationResult.value = null

    try {
      const result = await dataLoaderAPI.validateFile(file, entityType)
      validationResult.value = result

      if (result.success) {
        ElMessage.success('数据校验成功')
      } else {
        ElMessage.error('数据校验失败，请查看错误详情')
      }

      return result
    } catch (error) {
      console.error('数据校验失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '数据校验失败'
      ElMessage.error(errorMessage)
      validationResult.value = {
        success: false,
        error: errorMessage,
        validation_id: null
      }
      return validationResult.value
    } finally {
      validating.value = false
    }
  }

  // 下载验证失败报告
  const downloadValidationErrors = async (validationId) => {
    try {
      const blob = await dataLoaderAPI.downloadValidationErrors(validationId)

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `validation_errors_${validationId}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('错误报告下载成功')
    } catch (error) {
      console.error('下载验证错误报告失败:', error)
      ElMessage.error('下载失败')
    }
  }

  // 上传文件并创建任务
  const uploadFile = async (file, entityType) => {
    loading.value = true
    uploadProgress.value = 0
    
    try {
      const task = await dataLoaderAPI.uploadFile(file, entityType)
      tasks.value.unshift(task)
      ElMessage.success('文件上传成功，开始处理数据')
      return { success: true, task }
    } catch (error) {
      console.error('文件上传失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '文件上传失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
      uploadProgress.value = 0
    }
  }

  // 获取任务列表
  const getTasks = async (params = {}) => {
    loading.value = true
    try {
      const response = await dataLoaderAPI.getTasks(params)
      tasks.value = response || []
      return tasks.value
    } catch (error) {
      console.error('获取任务列表失败:', error)
      ElMessage.error('获取任务列表失败')
      tasks.value = []
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取任务详情
  const getTask = async (taskId) => {
    try {
      const task = await dataLoaderAPI.getTask(taskId)
      // 更新本地任务列表中的对应任务
      const index = tasks.value.findIndex(t => t.id === taskId)
      if (index !== -1) {
        tasks.value[index] = task
      }
      return task
    } catch (error) {
      console.error('获取任务详情失败:', error)
      ElMessage.error('获取任务详情失败')
      return null
    }
  }

  // 获取任务进度
  const getTaskProgress = async (taskId) => {
    try {
      const progress = await dataLoaderAPI.getTaskProgress(taskId)
      return progress
    } catch (error) {
      console.error('获取任务进度失败:', error)
      return null
    }
  }

  // 获取任务结果
  const getTaskResult = async (taskId) => {
    try {
      const result = await dataLoaderAPI.getTaskResult(taskId)
      return result
    } catch (error) {
      console.error('获取任务结果失败:', error)
      ElMessage.error('获取任务结果失败')
      return null
    }
  }

  // 轮询任务状态
  const pollTaskStatus = async (taskId, callback) => {
    const poll = async () => {
      try {
        const task = await getTask(taskId)
        if (task && callback) {
          callback(task)
        }
        
        // 如果任务还在处理中，继续轮询
        if (task && (task.status === 'pending' || task.status === 'processing')) {
          setTimeout(poll, 2000) // 每2秒轮询一次
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
      }
    }
    
    poll()
  }

  return {
    tasks,
    loading,
    uploadProgress,
    validationResult,
    validating,
    validateFile,
    downloadValidationErrors,
    uploadFile,
    getTasks,
    getTask,
    getTaskProgress,
    getTaskResult,
    pollTaskStatus
  }
})
