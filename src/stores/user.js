import { defineStore } from 'pinia'
import { ref } from 'vue'
import { usersAPI } from '@/api'

export const useUserStore = defineStore('user', () => {
  const users = ref([])
  const loading = ref(false)

  const getUsers = async (params = {}) => {
    loading.value = true
    try {
      const response = await usersAPI.getUsers(params)
      users.value = response || []
      return users.value
    } catch (error) {
      console.error('获取用户列表失败:', error)
      users.value = []
      return []
    } finally {
      loading.value = false
    }
  }

  const createUser = async (userData) => {
    loading.value = true
    try {
      const newUser = await usersAPI.createUser(userData)
      users.value.push(newUser)
      return { success: true, user: newUser }
    } catch (error) {
      console.error('创建用户失败:', error)
      let errorMessage = '创建用户失败'

      if (error.response?.data?.detail) {
        const detail = error.response.data.detail
        if (typeof detail === 'string') {
          errorMessage = detail
        } else if (Array.isArray(detail)) {
          const errors = detail.map(err => {
            if (typeof err === 'string') return err
            if (err && typeof err === 'object') {
              return err.msg || err.message || '验证错误'
            }
            return '验证错误'
          }).filter(Boolean)
          if (errors.length > 0) {
            errorMessage = errors.join(', ')
          }
        }
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (id, userData) => {
    loading.value = true
    try {
      const updatedUser = await usersAPI.updateUser(id, userData)
      const index = users.value.findIndex(user => user.id === id)
      if (index !== -1) {
        users.value[index] = updatedUser
      }
      return { success: true, user: updatedUser }
    } catch (error) {
      console.error('更新用户失败:', error)
      let errorMessage = '更新用户失败'

      if (error.response?.data?.detail) {
        const detail = error.response.data.detail
        if (typeof detail === 'string') {
          errorMessage = detail
        } else if (Array.isArray(detail)) {
          const errors = detail.map(err => {
            if (typeof err === 'string') return err
            if (err && typeof err === 'object') {
              return err.msg || err.message || '验证错误'
            }
            return '验证错误'
          }).filter(Boolean)
          if (errors.length > 0) {
            errorMessage = errors.join(', ')
          }
        }
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  const deleteUser = async (id) => {
    loading.value = true
    try {
      await usersAPI.deleteUser(id)
      const index = users.value.findIndex(user => user.id === id)
      if (index !== -1) {
        users.value.splice(index, 1)
      }
      return { success: true }
    } catch (error) {
      console.error('删除用户失败:', error)
      let errorMessage = '删除用户失败'

      if (error.response?.data?.detail) {
        const detail = error.response.data.detail
        if (typeof detail === 'string') {
          errorMessage = detail
        } else if (Array.isArray(detail)) {
          const errors = detail.map(err => {
            if (typeof err === 'string') return err
            if (err && typeof err === 'object') {
              return err.msg || err.message || '验证错误'
            }
            return '验证错误'
          }).filter(Boolean)
          if (errors.length > 0) {
            errorMessage = errors.join(', ')
          }
        }
      } else if (error.message) {
        errorMessage = error.message
      }

      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  // 获取单个用户详情
  const getUser = async (id) => {
    loading.value = true
    try {
      return await usersAPI.getUser(id)
    } catch (error) {
      console.error('获取用户详情失败:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  return {
    users,
    loading,
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser
  }
})
