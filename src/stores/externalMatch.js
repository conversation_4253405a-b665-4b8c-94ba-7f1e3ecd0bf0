import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { externalMatchAPI } from '@/api'

export const useExternalMatchStore = defineStore('externalMatch', () => {
  const tasks = ref([])
  const loading = ref(false)
  const uploadProgress = ref(0)

  // 上传文件创建外部匹配任务
  const uploadFile = async (file) => {
    loading.value = true
    uploadProgress.value = 0
    
    try {
      const task = await externalMatchAPI.uploadFile(file)
      tasks.value.unshift(task)
      ElMessage.success('文件上传成功，请审核文件内容')
      return { success: true, task }
    } catch (error) {
      console.error('文件上传失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '文件上传失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
      uploadProgress.value = 0
    }
  }

  // 获取文件审核信息
  const getFileReview = async (taskId) => {
    try {
      return await externalMatchAPI.getFileReview(taskId)
    } catch (error) {
      console.error('获取文件审核信息失败:', error)
      ElMessage.error('获取文件审核信息失败')
      return null
    }
  }

  // 确认文件并开始匹配
  const confirmAndStartMatching = async (taskId) => {
    loading.value = true
    try {
      const task = await externalMatchAPI.confirmAndStartMatching(taskId)
      // 更新本地任务状态
      const index = tasks.value.findIndex(t => t.id === taskId)
      if (index !== -1) {
        tasks.value[index] = task
      }
      ElMessage.success('文件确认成功，开始匹配处理')
      return { success: true, task }
    } catch (error) {
      console.error('确认文件失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '确认文件失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  // 获取匹配进度
  const getMatchProgress = async (taskId) => {
    try {
      return await externalMatchAPI.getMatchProgress(taskId)
    } catch (error) {
      console.error('获取匹配进度失败:', error)
      return null
    }
  }

  // 获取匹配结果
  const getMatchResult = async (taskId) => {
    try {
      return await externalMatchAPI.getMatchResult(taskId)
    } catch (error) {
      console.error('获取匹配结果失败:', error)
      ElMessage.error('获取匹配结果失败')
      return null
    }
  }

  // 开始数据分发
  const startDistribution = async (taskId) => {
    loading.value = true
    try {
      const task = await externalMatchAPI.startDistribution(taskId)
      // 更新本地任务状态
      const index = tasks.value.findIndex(t => t.id === taskId)
      if (index !== -1) {
        tasks.value[index] = task
      }
      ElMessage.success('开始数据分发')
      return { success: true, task }
    } catch (error) {
      console.error('开始分发失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '开始分发失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  // 获取分发进度
  const getDistributionProgress = async (taskId) => {
    try {
      return await externalMatchAPI.getDistributionProgress(taskId)
    } catch (error) {
      console.error('获取分发进度失败:', error)
      return null
    }
  }

  // 下载失败记录
  const downloadErrorRecords = async (taskId) => {
    try {
      const blob = await externalMatchAPI.downloadErrorRecords(taskId)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `error_records_${taskId}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('失败记录下载成功')
      return { success: true }
    } catch (error) {
      console.error('下载失败记录失败:', error)
      const errorMessage = error.response?.data?.detail || error.message || '下载失败记录失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // 轮询任务状态
  const pollTaskStatus = async (taskId, callback) => {
    const poll = async () => {
      try {
        const progress = await getMatchProgress(taskId)

        if (progress && callback) {
          callback(progress)
        }

        // 如果任务还在处理中，继续轮询
        const processingStatuses = ['UPLOADED', 'PENDING', 'REVIEWING', 'PROCESSING']
        const shouldContinue = progress && processingStatuses.includes(progress.status)

        if (shouldContinue) {
          setTimeout(poll, 2000) // 每2秒轮询一次
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
      }
    }

    poll()
  }

  return {
    tasks,
    loading,
    uploadProgress,
    uploadFile,
    getFileReview,
    confirmAndStartMatching,
    getMatchProgress,
    getMatchResult,
    startDistribution,
    getDistributionProgress,
    downloadErrorRecords,
    pollTaskStatus
  }
})
