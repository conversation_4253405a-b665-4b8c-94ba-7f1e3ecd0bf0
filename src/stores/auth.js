import { defineStore } from 'pinia'
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const isAuthenticated = ref(!!token.value)

  const login = async (credentials) => {
    try {
      // 调用真实的登录API
      const response = await authAPI.login(credentials)

      if (response.access_token) {
        token.value = response.access_token
        isAuthenticated.value = true
        localStorage.setItem('token', response.access_token)

        // 获取用户信息
        try {
          const userInfo = await authAPI.getCurrentUser()
          user.value = userInfo
          localStorage.setItem('user', JSON.stringify(userInfo))
        } catch (userError) {
          console.warn('获取用户信息失败:', userError)
        }

        ElMessage.success('登录成功')
        return { success: true, user: user.value }
      } else {
        // 直接返回错误而不是抛出异常
        const errorMessage = '登录失败：未获取到访问令牌'
        console.error('登录错误:', errorMessage)
        ElMessage.error(errorMessage)
        return { success: false, error: errorMessage }
      }
    } catch (error) {
      console.error('登录错误:', error)
      const errorMessage = error.response?.data?.detail || error.message || '登录失败'
      ElMessage.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  const logout = async () => {
    try {
      // 调用后端登出API
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.warn('登出API调用失败:', error)
    } finally {
      // 清除响应式状态
      user.value = null
      token.value = null
      isAuthenticated.value = false

      // 显示成功消息
      ElMessage.success('已退出登录')

      // 确保状态更新完成
      await nextTick()
    }
  }

  const initAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')

    if (savedToken) {
      token.value = savedToken
      isAuthenticated.value = true

      if (savedUser) {
        user.value = JSON.parse(savedUser)
      }

      // 验证token是否仍然有效
      try {
        const userInfo = await authAPI.getCurrentUser()
        user.value = userInfo
        localStorage.setItem('user', JSON.stringify(userInfo))
      } catch (error) {
        console.warn('Token验证失败，清除认证状态:', error)
        logout()
      }
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()
      if (response.access_token) {
        token.value = response.access_token
        localStorage.setItem('token', response.access_token)
        return true
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      return false
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    initAuth,
    refreshToken
  }
})
