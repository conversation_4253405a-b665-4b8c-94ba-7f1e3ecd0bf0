import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { API_CONFIG } from './config'

// 创建axios实例
const http = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: API_CONFIG.headers
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 如果是文件上传，修改Content-Type
    if (config.data instanceof FormData) {
      config.headers['Content-Type'] = 'multipart/form-data'
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.config.url, response.data)
    return response.data
  },
  (error) => {
    console.error('API错误:', error.response?.data || error.message)

    const authStore = useAuthStore()

    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response
      console.log('Error response data:', data, 'Type:', typeof data)


      
      switch (status) {
        case 401:
          ElMessage.error('认证失败，请重新登录')
          authStore.logout()
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          // 处理验证错误
          let errorMessage = '请求参数错误'
          if (data.detail && Array.isArray(data.detail)) {
            const errors = data.detail.map(err => {
              if (typeof err === 'string') return err
              if (err && typeof err === 'object') {
                return err.msg || err.message || '验证错误'
              }
              return '验证错误'
            }).filter(Boolean)
            if (errors.length > 0) {
              errorMessage = `参数错误: ${errors.join(', ')}`
            }
          } else if (data.detail && typeof data.detail === 'string') {
            errorMessage = `参数错误: ${data.detail}`
          }
          ElMessage.error(errorMessage)
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          let message = '请求失败'
          if (data && typeof data.message === 'string') {
            message = data.message
          } else if (data && typeof data === 'string') {
            message = data
          }
          ElMessage.error(message)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default http
