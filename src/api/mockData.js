/**
 * Mock数据存储 - 用于模拟后台API响应
 * 这个文件包含了所有模块的mock数据，使其不需要与后台交互
 */

// 用户相关Mock数据
export const MOCK_USERS = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: '系统管理员',
    role: 'admin',
    is_active: true,
    createTime: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    full_name: '普通用户1',
    role: 'user',
    is_active: true,
    createTime: '2024-01-02',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'viewer1',
    email: '<EMAIL>',
    full_name: '查看者1',
    role: 'viewer',
    is_active: true,
    createTime: '2024-01-03',
    created_at: '2024-01-03T00:00:00Z'
  },
  {
    id: 4,
    username: 'user2',
    email: '<EMAIL>',
    full_name: '普通用户2',
    role: 'user',
    is_active: true,
    createTime: '2024-01-04',
    created_at: '2024-01-04T00:00:00Z'
  },
  {
    id: 5,
    username: 'viewer2',
    email: '<EMAIL>',
    full_name: '查看者2',
    role: 'viewer',
    is_active: false,
    createTime: '2024-01-05',
    created_at: '2024-01-05T00:00:00Z'
  }
]

// Mock登录凭据 (用户名: 密码)
export const MOCK_CREDENTIALS = {
  'admin': 'admin',
  'user1': 'password',
  'viewer1': 'password',
  'user2': 'password',
  'viewer2': 'password',
  'test': 'test'
}

// 数据加载器Mock数据
export const MOCK_TASKS = []
export let nextTaskId = 1

// 验证结果Mock数据
export const MOCK_VALIDATIONS = {}
export let nextValidationId = 1

// 用户ID计数器
export let nextUserId = 6

// 角色显示名称映射
export const ROLE_DISPLAY_NAMES = {
  'admin': '管理员',
  'user': '普通用户',
  'viewer': '查看者'
}

// 实体类型显示名称映射
export const ENTITY_TYPE_DISPLAY_NAMES = {
  'product': '产品',
  'hco': '医疗机构'
}

// 任务状态显示名称映射
export const TASK_STATUS_DISPLAY_NAMES = {
  'pending': '等待中',
  'processing': '处理中',
  'success': '成功',
  'failed': '失败'
}

/**
 * 工具函数：模拟网络延迟
 */
export function mockDelay(min = 300, max = 1000) {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * 工具函数：生成随机ID
 */
export function generateMockId() {
  return Math.floor(Math.random() * 10000) + 1000
}

/**
 * 工具函数：格式化日期
 */
export function formatDate(date = new Date()) {
  return date.toISOString().split('T')[0]
}

/**
 * 工具函数：生成Mock Token
 */
export function generateMockToken(username) {
  return `mock_token_${username}_${Date.now()}`
}

/**
 * 工具函数：从Token中提取用户名
 */
export function extractUsernameFromToken(token) {
  if (!token) return null
  const parts = token.split('_')
  return parts.length >= 3 ? parts[2] : null
}

/**
 * 工具函数：验证文件类型
 */
export function validateFileType(filename) {
  const allowedTypes = ['.csv', '.xlsx', '.xls']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return allowedTypes.includes(extension)
}

/**
 * 工具函数：验证文件大小
 */
export function validateFileSize(size, maxSizeMB = 10) {
  const maxSize = maxSizeMB * 1024 * 1024
  return size <= maxSize
}

/**
 * 工具函数：生成CSV错误报告
 */
export function generateErrorReport() {
  const errorData = [
    ['行号', '字段名', '错误类型', '错误描述', '当前值', '建议值'],
    ['2', '产品名称', '必填字段缺失', '产品名称不能为空', '', '请填写产品名称'],
    ['3', '产品编码', '格式错误', '产品编码格式不正确', 'ABC-123', 'ABC123（不能包含特殊字符）'],
    ['5', '价格', '数据类型错误', '价格必须为数字', '免费', '0 或具体数字'],
    ['7', '生产日期', '日期格式错误', '日期格式应为YYYY-MM-DD', '2024/01/01', '2024-01-01'],
    ['9', '分类', '枚举值错误', '分类值不在允许范围内', '其他', '药品/器械/耗材'],
    ['12', '供应商', '必填字段缺失', '供应商信息不能为空', '', '请填写供应商名称'],
    ['15', '规格', '格式错误', '规格格式不正确', '100ml*10', '100ml×10'],
    ['18', '有效期', '日期格式错误', '有效期格式应为YYYY-MM-DD', '2025年12月', '2025-12-31'],
    ['20', '批准文号', '格式错误', '批准文号格式不正确', 'H12345678', '国药准字H12345678']
  ]
  
  return errorData.map(row => row.join(',')).join('\n')
}

/**
 * 工具函数：创建Blob文件
 */
export function createCSVBlob(content) {
  return new Blob(['\ufeff' + content], {
    type: 'text/csv;charset=utf-8'
  })
}

// 导出计数器更新函数
export function incrementTaskId() {
  return ++nextTaskId
}

export function incrementValidationId() {
  return ++nextValidationId
}

export function incrementUserId() {
  return ++nextUserId
}

// 重置Mock数据的函数（用于测试）
export function resetMockData() {
  MOCK_TASKS.length = 0
  Object.keys(MOCK_VALIDATIONS).forEach(key => delete MOCK_VALIDATIONS[key])
  nextTaskId = 1
  nextValidationId = 1
  nextUserId = 6
}

// 获取当前Mock数据状态的函数（用于调试）
export function getMockDataStatus() {
  return {
    users: MOCK_USERS.length,
    tasks: MOCK_TASKS.length,
    validations: Object.keys(MOCK_VALIDATIONS).length,
    nextTaskId,
    nextValidationId,
    nextUserId
  }
}
