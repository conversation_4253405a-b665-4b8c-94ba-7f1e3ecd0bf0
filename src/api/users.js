import http from './http'
import { API_ENDPOINTS } from './config'

// Mock用户数据存储
let MOCK_USERS = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: '系统管理员',
    role: 'admin',
    is_active: true,
    createTime: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    full_name: '普通用户1',
    role: 'user',
    is_active: true,
    createTime: '2024-01-02',
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'viewer1',
    email: '<EMAIL>',
    full_name: '查看者1',
    role: 'viewer',
    is_active: true,
    createTime: '2024-01-03',
    created_at: '2024-01-03T00:00:00Z'
  },
  {
    id: 4,
    username: 'user2',
    email: '<EMAIL>',
    full_name: '普通用户2',
    role: 'user',
    is_active: true,
    createTime: '2024-01-04',
    created_at: '2024-01-04T00:00:00Z'
  },
  {
    id: 5,
    username: 'viewer2',
    email: '<EMAIL>',
    full_name: '查看者2',
    role: 'viewer',
    is_active: false,
    createTime: '2024-01-05',
    created_at: '2024-01-05T00:00:00Z'
  }
]

// 用于生成新用户ID
let nextUserId = 6

/**
 * 用户管理相关API - Mock实现
 */
export const usersAPI = {
  /**
   * 获取用户列表 - Mock实现
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过的记录数
   * @param {number} params.limit - 返回的记录数限制
   * @returns {Promise<Array>} 用户列表
   */
  async getUsers(params = {}) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const { skip = 0, limit = 100 } = params

    // 模拟分页
    const startIndex = skip
    const endIndex = Math.min(skip + limit, MOCK_USERS.length)
    const paginatedUsers = MOCK_USERS.slice(startIndex, endIndex)

    return paginatedUsers
  },

  /**
   * 创建用户 - Mock实现
   * @param {Object} userData - 用户数据
   * @param {string} userData.username - 用户名
   * @param {string} userData.password - 密码
   * @param {string} [userData.email] - 邮箱
   * @param {string} [userData.full_name] - 全名
   * @param {string} [userData.role] - 角色 (admin/user/viewer)
   * @param {boolean} [userData.is_active] - 是否激活
   * @returns {Promise<Object>} 创建的用户信息
   */
  async createUser(userData) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 验证必填字段
    if (!userData.username) {
      throw new Error('用户名不能为空')
    }
    if (!userData.password) {
      throw new Error('密码不能为空')
    }
    if (!userData.email) {
      throw new Error('邮箱不能为空')
    }

    // 检查用户名是否已存在
    const existingUser = MOCK_USERS.find(user => user.username === userData.username)
    if (existingUser) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否已存在
    const existingEmail = MOCK_USERS.find(user => user.email === userData.email)
    if (existingEmail) {
      throw new Error('邮箱已存在')
    }

    // 创建新用户
    const newUser = {
      id: nextUserId++,
      username: userData.username,
      email: userData.email,
      full_name: userData.full_name || userData.username,
      role: userData.role || 'user',
      is_active: userData.is_active !== undefined ? userData.is_active : true,
      createTime: new Date().toISOString().split('T')[0],
      created_at: new Date().toISOString()
    }

    MOCK_USERS.push(newUser)

    // 返回创建的用户（不包含密码）
    const { password, ...userWithoutPassword } = newUser
    return userWithoutPassword
  },

  /**
   * 获取用户详情 - Mock实现
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 用户详情
   */
  async getUser(userId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    const user = MOCK_USERS.find(u => u.id === parseInt(userId))
    if (!user) {
      throw new Error('用户不存在')
    }

    return user
  },

  /**
   * 更新用户 - Mock实现
   * @param {number} userId - 用户ID
   * @param {Object} userData - 更新的用户数据
   * @returns {Promise<Object>} 更新后的用户信息
   */
  async updateUser(userId, userData) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    const userIndex = MOCK_USERS.findIndex(u => u.id === parseInt(userId))
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    const existingUser = MOCK_USERS[userIndex]

    // 如果更新用户名，检查是否已存在
    if (userData.username && userData.username !== existingUser.username) {
      const duplicateUser = MOCK_USERS.find(user => user.username === userData.username)
      if (duplicateUser) {
        throw new Error('用户名已存在')
      }
    }

    // 如果更新邮箱，检查是否已存在
    if (userData.email && userData.email !== existingUser.email) {
      const duplicateEmail = MOCK_USERS.find(user => user.email === userData.email)
      if (duplicateEmail) {
        throw new Error('邮箱已存在')
      }
    }

    // 更新用户信息
    const updatedUser = {
      ...existingUser,
      ...userData,
      id: existingUser.id, // 确保ID不被覆盖
      created_at: existingUser.created_at // 确保创建时间不被覆盖
    }

    MOCK_USERS[userIndex] = updatedUser

    return updatedUser
  },

  /**
   * 删除用户 - Mock实现
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>}
   */
  async deleteUser(userId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 600))

    const userIndex = MOCK_USERS.findIndex(u => u.id === parseInt(userId))
    if (userIndex === -1) {
      throw new Error('用户不存在')
    }

    const user = MOCK_USERS[userIndex]

    // 防止删除admin用户
    if (user.username === 'admin') {
      throw new Error('不能删除管理员用户')
    }

    MOCK_USERS.splice(userIndex, 1)

    return {
      message: '用户删除成功'
    }
  }
}
