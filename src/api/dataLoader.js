import http from './http'
import { API_ENDPOINTS } from './config'

// Mock任务数据存储
let MOCK_TASKS = []
let nextTaskId = 1

// Mock验证结果存储
let MOCK_VALIDATIONS = {}
let nextValidationId = 1

/**
 * 数据加载器相关API - Mock实现
 */
export const dataLoaderAPI = {
  /**
   * 验证文件数据 (Mock实现)
   * @param {File} file - 上传的文件
   * @param {string} entityType - 实体类型 (product/hco)
   * @returns {Promise<Object>} 验证结果
   */
  async validateFile(file, entityType) {
    // Mock数据校验逻辑
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟不同的校验结果
        const fileName = file.name.toLowerCase()
        const fileSize = file.size

        // 模拟校验逻辑：
        // 1. 文件名包含"error"或"fail"时模拟校验失败
        // 2. 文件大小超过10MB时模拟校验失败
        // 3. 其他情况模拟校验成功

        if (fileName.includes('error') || fileName.includes('fail') || fileSize >  1024 * 1024) {
          // 模拟校验失败
          resolve({
            success: false,
            validation_id: Math.floor(Math.random() * 10000),
            error: '数据校验失败：发现格式错误或必填字段缺失',
            error_count: Math.floor(Math.random() * 50) + 1,
            details: {
              format_errors: Math.floor(Math.random() * 20) + 1,
              missing_fields: Math.floor(Math.random() * 15) + 1,
              invalid_values: Math.floor(Math.random() * 15) + 1
            }
          })
        } else {
          // 模拟校验成功
          resolve({
            success: true,
            validation_id: Math.floor(Math.random() * 10000),
            message: '数据校验成功，所有数据格式正确',
            record_count: Math.floor(Math.random() * 1000) + 100,
            details: {
              total_records: Math.floor(Math.random() * 1000) + 100,
              valid_records: Math.floor(Math.random() * 1000) + 100,
              entity_type: entityType
            }
          })
        }
      }, 2000 + Math.random() * 2000) // 2-4秒的随机延迟模拟校验时间
    })
  },

  /**
   * 下载验证失败报告 (Mock实现)
   * @param {number} validationId - 验证ID
   * @returns {Promise<Blob>} 错误报告文件
   */
  async downloadValidationErrors(validationId) {
    // Mock生成错误报告Excel文件
    return new Promise((resolve) => {
      setTimeout(() => {
        // 创建一个简单的CSV格式错误报告
        const errorData = [
          ['行号', '字段名', '错误类型', '错误描述', '当前值', '建议值'],
          ['2', '产品名称', '必填字段缺失', '产品名称不能为空', '', '请填写产品名称'],
          ['3', '产品编码', '格式错误', '产品编码格式不正确', 'ABC-123', 'ABC123（不能包含特殊字符）'],
          ['5', '价格', '数据类型错误', '价格必须为数字', '免费', '0 或具体数字'],
          ['7', '生产日期', '日期格式错误', '日期格式应为YYYY-MM-DD', '2024/01/01', '2024-01-01'],
          ['9', '分类', '枚举值错误', '分类值不在允许范围内', '其他', '药品/器械/耗材'],
        ]

        // 转换为CSV格式
        const csvContent = errorData.map(row => row.join(',')).join('\n')

        // 创建Blob对象
        const blob = new Blob(['\ufeff' + csvContent], {
          type: 'text/csv;charset=utf-8'
        })

        resolve(blob)
      }, 1000) // 1秒延迟模拟下载准备时间
    })
  },

  /**
   * 上传文件并创建数据加载任务 - Mock实现
   * @param {File} file - 上传的文件
   * @param {string} entityType - 实体类型 (product/hco)
   * @returns {Promise<Object>} 创建的任务信息
   */
  async uploadFile(file, entityType) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 验证文件
    if (!file) {
      throw new Error('请选择要上传的文件')
    }

    // 验证文件类型
    const allowedTypes = ['.csv', '.xlsx', '.xls']
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    if (!allowedTypes.includes(fileExtension)) {
      throw new Error('不支持的文件格式，请上传CSV或Excel文件')
    }

    // 验证文件大小 (10MB限制)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      throw new Error('文件大小超过10MB限制')
    }

    // 验证实体类型
    if (!['product', 'hco'].includes(entityType)) {
      throw new Error('无效的实体类型')
    }

    // 创建新任务
    const newTask = {
      id: nextTaskId++,
      filename: file.name,
      entity_type: entityType,
      file_size: file.size,
      status: 'pending',
      progress: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      message: '任务已创建，等待处理',
      total_records: 0,
      processed_records: 0,
      success_records: 0,
      failed_records: 0
    }

    MOCK_TASKS.unshift(newTask)

    // 模拟异步处理任务状态更新
    setTimeout(() => {
      updateTaskStatus(newTask.id, 'processing', 25, '正在处理数据...')
    }, 2000)

    setTimeout(() => {
      updateTaskStatus(newTask.id, 'processing', 75, '数据处理中...')
    }, 4000)

    setTimeout(() => {
      // 根据文件名决定成功或失败
      const shouldFail = file.name.toLowerCase().includes('error') || file.name.toLowerCase().includes('fail')
      if (shouldFail) {
        updateTaskStatus(newTask.id, 'failed', 100, '数据处理失败', {
          total_records: 100,
          processed_records: 100,
          success_records: 0,
          failed_records: 100
        })
      } else {
        updateTaskStatus(newTask.id, 'success', 100, '数据处理完成', {
          total_records: Math.floor(Math.random() * 500) + 100,
          processed_records: Math.floor(Math.random() * 500) + 100,
          success_records: Math.floor(Math.random() * 500) + 100,
          failed_records: Math.floor(Math.random() * 10)
        })
      }
    }, 6000)

    return newTask
  },

  /**
   * 获取数据加载任务列表 - Mock实现
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过的记录数
   * @param {number} params.limit - 返回的记录数限制
   * @returns {Promise<Array>} 任务列表
   */
  async getTasks(params = {}) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const { skip = 0, limit = 100 } = params

    // 模拟分页
    const startIndex = skip
    const endIndex = Math.min(skip + limit, MOCK_TASKS.length)
    const paginatedTasks = MOCK_TASKS.slice(startIndex, endIndex)

    return paginatedTasks
  },

  /**
   * 获取任务详情 - Mock实现
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 任务详情
   */
  async getTask(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    const task = MOCK_TASKS.find(t => t.id === parseInt(taskId))
    if (!task) {
      throw new Error('任务不存在')
    }

    return task
  },

  /**
   * 获取任务进度 - Mock实现
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 任务进度信息
   */
  async getTaskProgress(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))

    const task = MOCK_TASKS.find(t => t.id === parseInt(taskId))
    if (!task) {
      throw new Error('任务不存在')
    }

    return {
      task_id: task.id,
      status: task.status,
      progress: task.progress,
      message: task.message,
      updated_at: task.updated_at
    }
  },

  /**
   * 获取任务结果 - Mock实现
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 任务结果
   */
  async getTaskResult(taskId) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    const task = MOCK_TASKS.find(t => t.id === parseInt(taskId))
    if (!task) {
      throw new Error('任务不存在')
    }

    if (task.status !== 'success' && task.status !== 'failed') {
      throw new Error('任务尚未完成')
    }

    return {
      task_id: task.id,
      status: task.status,
      total_records: task.total_records,
      processed_records: task.processed_records,
      success_records: task.success_records,
      failed_records: task.failed_records,
      result_file_url: task.status === 'success' ? `/downloads/result_${task.id}.xlsx` : null,
      error_file_url: task.failed_records > 0 ? `/downloads/errors_${task.id}.xlsx` : null,
      completed_at: task.updated_at
    }
  }
}

/**
 * 更新任务状态的辅助函数
 */
function updateTaskStatus(taskId, status, progress, message, additionalData = {}) {
  const taskIndex = MOCK_TASKS.findIndex(t => t.id === taskId)
  if (taskIndex !== -1) {
    MOCK_TASKS[taskIndex] = {
      ...MOCK_TASKS[taskIndex],
      status,
      progress,
      message,
      updated_at: new Date().toISOString(),
      ...additionalData
    }
  }
}
