import http from './http'
import { API_ENDPOINTS } from './config'

/**
 * 系统相关API
 */
export const systemAPI = {
  /**
   * 获取系统信息
   * @returns {Promise<Object>} 系统信息
   */
  async getSystemInfo() {
    const response = await http.get(API_ENDPOINTS.SYSTEM.INFO)
    return response
  },

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    const response = await http.get(API_ENDPOINTS.SYSTEM.HEALTH)
    return response
  },

  /**
   * 个性化问候
   * @param {string} name - 名称
   * @returns {Promise<Object>} 问候信息
   */
  async sayHello(name) {
    const response = await http.get(API_ENDPOINTS.SYSTEM.HELLO(name))
    return response
  }
}
