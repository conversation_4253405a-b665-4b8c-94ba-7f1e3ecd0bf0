import http from './http'
import { API_ENDPOINTS } from './config'

// Mock用户数据
const MOCK_USERS = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: '系统管理员',
    role: 'admin',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    full_name: '普通用户1',
    role: 'user',
    is_active: true,
    created_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'viewer1',
    email: '<EMAIL>',
    full_name: '查看者1',
    role: 'viewer',
    is_active: true,
    created_at: '2024-01-03T00:00:00Z'
  }
]

// Mock登录凭据 (用户名: 密码)
const MOCK_CREDENTIALS = {
  'admin': 'admin',
  'user1': 'password',
  'viewer1': 'password',
  'test': 'test'
}

/**
 * 认证相关API - Mock实现
 */
export const authAPI = {
  /**
   * 用户登录 - Mock实现
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<Object>} 返回token信息
   */
  async login(credentials) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    const { username, password } = credentials

    // 验证用户名和密码
    if (!MOCK_CREDENTIALS[username] || MOCK_CREDENTIALS[username] !== password) {
      throw new Error('用户名或密码错误')
    }

    // 生成mock token
    const mockToken = `mock_token_${username}_${Date.now()}`

    return {
      access_token: mockToken,
      token_type: 'bearer',
      expires_in: 3600
    }
  },

  /**
   * 用户登出 - Mock实现
   * @returns {Promise<Object>}
   */
  async logout() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    return {
      message: '登出成功'
    }
  },

  /**
   * 获取当前用户信息 - Mock实现
   * @returns {Promise<Object>} 用户信息
   */
  async getCurrentUser() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 从localStorage获取token来确定当前用户
    const token = localStorage.getItem('token')
    if (!token) {
      throw new Error('未找到认证令牌')
    }

    // 从token中提取用户名 (mock_token_username_timestamp)
    const tokenParts = token.split('_')
    if (tokenParts.length < 3) {
      throw new Error('无效的认证令牌')
    }

    const username = tokenParts[2]
    const user = MOCK_USERS.find(u => u.username === username)

    if (!user) {
      throw new Error('用户不存在')
    }

    return user
  },

  /**
   * 刷新访问令牌 - Mock实现
   * @returns {Promise<Object>} 新的token信息
   */
  async refreshToken() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 从localStorage获取当前token
    const currentToken = localStorage.getItem('token')
    if (!currentToken) {
      throw new Error('未找到认证令牌')
    }

    // 从token中提取用户名
    const tokenParts = currentToken.split('_')
    if (tokenParts.length < 3) {
      throw new Error('无效的认证令牌')
    }

    const username = tokenParts[2]

    // 生成新的token
    const newToken = `mock_token_${username}_${Date.now()}`

    return {
      access_token: newToken,
      token_type: 'bearer',
      expires_in: 3600
    }
  }
}
