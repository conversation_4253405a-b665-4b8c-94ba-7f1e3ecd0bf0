import http from './http'
import { API_ENDPOINTS } from './config'

/**
 * 外部匹配相关API
 */
export const externalMatchAPI = {
  /**
   * 上传文件创建外部匹配任务
   * @param {File} file - 上传的文件
   * @returns {Promise<Object>} 创建的任务信息
   */
  async uploadFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await http.post(API_ENDPOINTS.EXTERNAL_MATCH.UPLOAD, formData)
    return response
  },

  /**
   * 获取文件审核信息
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 文件审核信息
   */
  async getFileReview(taskId) {
    const response = await http.get(API_ENDPOINTS.EXTERNAL_MATCH.REVIEW(taskId))
    return response
  },

  /**
   * 确认文件并开始匹配
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 更新后的任务信息
   */
  async confirmAndStartMatching(taskId) {
    const response = await http.post(API_ENDPOINTS.EXTERNAL_MATCH.CONFIRM(taskId))
    return response
  },

  /**
   * 获取匹配进度
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 匹配进度信息
   */
  async getMatchProgress(taskId) {
    const response = await http.get(API_ENDPOINTS.EXTERNAL_MATCH.PROGRESS(taskId))
    return response
  },

  /**
   * 获取匹配结果
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 匹配结果
   */
  async getMatchResult(taskId) {
    const response = await http.get(API_ENDPOINTS.EXTERNAL_MATCH.RESULT(taskId))
    return response
  },

  /**
   * 开始数据分发
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 更新后的任务信息
   */
  async startDistribution(taskId) {
    const response = await http.post(API_ENDPOINTS.EXTERNAL_MATCH.DISTRIBUTE(taskId))
    return response
  },

  /**
   * 获取分发进度
   * @param {number} taskId - 任务ID
   * @returns {Promise<Object>} 分发进度信息
   */
  async getDistributionProgress(taskId) {
    const response = await http.get(API_ENDPOINTS.EXTERNAL_MATCH.DISTRIBUTION(taskId))
    return response
  },

  /**
   * 下载失败记录
   * @param {number} taskId - 任务ID
   * @returns {Promise<Blob>} 文件数据
   */
  async downloadErrorRecords(taskId) {
    const response = await http.get(API_ENDPOINTS.EXTERNAL_MATCH.DOWNLOAD_ERRORS(taskId), {
      responseType: 'blob'
    })
    return response
  }
}
