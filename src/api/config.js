// API配置
export const API_CONFIG = {
  baseURL: 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
}

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    LOGOUT: '/api/v1/auth/logout',
    ME: '/api/v1/auth/me',
    REFRESH: '/api/v1/auth/refresh'
  },
  
  // 用户管理
  USERS: {
    LIST: '/api/v1/users/',
    CREATE: '/api/v1/users/',
    GET: (id) => `/api/v1/users/${id}`,
    UPDATE: (id) => `/api/v1/users/${id}`,
    DELETE: (id) => `/api/v1/users/${id}`
  },
  
  // 数据加载器
  DATA_LOADER: {
    UPLOAD: '/api/v1/data-loader/upload',
    VALIDATE: '/api/v1/data-loader/validate',
    DOWNLOAD_VALIDATION_ERRORS: (id) => `/api/v1/data-loader/validation/${id}/download-errors`,
    TASKS: '/api/v1/data-loader/tasks',
    TASK_DETAIL: (id) => `/api/v1/data-loader/tasks/${id}`,
    TASK_PROGRESS: (id) => `/api/v1/data-loader/tasks/${id}/progress`,
    TASK_RESULT: (id) => `/api/v1/data-loader/tasks/${id}/result`
  },
  
  // 外部匹配
  EXTERNAL_MATCH: {
    UPLOAD: '/api/v1/external-match/upload',
    REVIEW: (id) => `/api/v1/external-match/tasks/${id}/review`,
    CONFIRM: (id) => `/api/v1/external-match/tasks/${id}/confirm`,
    PROGRESS: (id) => `/api/v1/external-match/tasks/${id}/progress`,
    RESULT: (id) => `/api/v1/external-match/tasks/${id}/result`,
    DISTRIBUTE: (id) => `/api/v1/external-match/tasks/${id}/distribute`,
    DISTRIBUTION: (id) => `/api/v1/external-match/tasks/${id}/distribution`,
    DOWNLOAD_ERRORS: (id) => `/api/v1/external-match/tasks/${id}/download-errors`
  },
  
  // 系统信息
  SYSTEM: {
    INFO: '/',
    HEALTH: '/health',
    HELLO: (name) => `/hello/${name}`
  }
}

// 状态枚举
export const STATUS = {
  PROCESS: {
    PENDING: 'pending',
    PROCESSING: 'processing',
    SUCCESS: 'success',
    FAILED: 'failed'
  },
  MATCH: {
    UPLOADED: 'UPLOADED',
    REVIEWING: 'REVIEWING',
    PROCESSING: 'PROCESSING',
    MATCHED: 'MATCHED',
    DISTRIBUTING: 'DISTRIBUTING',
    COMPLETED: 'COMPLETED',
    FAILED: 'FAILED'
  },
  ENTITY_TYPE: {
    PRODUCT: 'product',
    HCO: 'hco'
  },
  USER_ROLE: {
    ADMIN: 'admin',
    USER: 'user',
    VIEWER: 'viewer'
  },
  SYSTEM_NODE: {
    CRM: 'crm',
    ERP: 'erp',
    MDM: 'mdm',
    DATA_WAREHOUSE: 'data_warehouse'
  }
}
