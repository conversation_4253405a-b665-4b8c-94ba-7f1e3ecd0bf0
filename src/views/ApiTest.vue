<template>
  <div class="api-test">
    <div class="page-header">
      <h1>API集成测试</h1>
      <p>测试与后端API的连接状态</p>
    </div>

    <el-row :gutter="20">
      <!-- 系统信息测试 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息测试</span>
          </template>
          <div class="test-section">
            <el-button type="primary" @click="testSystemInfo" :loading="loading.system">
              测试系统信息API
            </el-button>
            <div v-if="results.system" class="result">
              <h4>结果:</h4>
              <pre>{{ JSON.stringify(results.system, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 健康检查测试 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>健康检查测试</span>
          </template>
          <div class="test-section">
            <el-button type="success" @click="testHealthCheck" :loading="loading.health">
              测试健康检查API
            </el-button>
            <div v-if="results.health" class="result">
              <h4>结果:</h4>
              <pre>{{ JSON.stringify(results.health, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 认证测试 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>认证测试</span>
          </template>
          <div class="test-section">
            <el-form :model="loginForm" inline>
              <el-form-item label="用户名:">
                <el-input v-model="loginForm.username" placeholder="输入用户名" />
              </el-form-item>
              <el-form-item label="密码:">
                <el-input v-model="loginForm.password" type="password" placeholder="输入密码" />
              </el-form-item>
              <el-form-item>
                <el-button type="warning" @click="testLogin" :loading="loading.login">
                  测试登录API
                </el-button>
              </el-form-item>
            </el-form>
            <div v-if="results.login" class="result">
              <h4>登录结果:</h4>
              <pre>{{ JSON.stringify(results.login, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 用户管理测试 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>用户管理测试</span>
          </template>
          <div class="test-section">
            <el-button type="info" @click="testGetUsers" :loading="loading.users">
              测试获取用户列表API
            </el-button>
            <div v-if="results.users" class="result">
              <h4>用户列表结果:</h4>
              <pre>{{ JSON.stringify(results.users, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- API配置信息 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>API配置信息</span>
          </template>
          <div class="config-info">
            <p><strong>API基础URL:</strong> {{ apiConfig.baseURL }}</p>
            <p><strong>超时时间:</strong> {{ apiConfig.timeout }}ms</p>
            <p><strong>当前认证状态:</strong> 
              <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
                {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
              </el-tag>
            </p>
            <p v-if="authStore.user"><strong>当前用户:</strong> {{ authStore.user.username }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { systemAPI, usersAPI, API_CONFIG } from '@/api'

const authStore = useAuthStore()
const apiConfig = API_CONFIG

const loading = reactive({
  system: false,
  health: false,
  login: false,
  users: false
})

const results = reactive({
  system: null,
  health: null,
  login: null,
  users: null
})

const loginForm = reactive({
  username: 'admin',
  password: 'admin'
})

// 测试系统信息API
const testSystemInfo = async () => {
  loading.system = true
  try {
    const result = await systemAPI.getSystemInfo()
    results.system = result
    ElMessage.success('系统信息API测试成功')
  } catch (error) {
    console.error('系统信息API测试失败:', error)
    results.system = { error: error.message }
    ElMessage.error('系统信息API测试失败')
  } finally {
    loading.system = false
  }
}

// 测试健康检查API
const testHealthCheck = async () => {
  loading.health = true
  try {
    const result = await systemAPI.healthCheck()
    results.health = result
    ElMessage.success('健康检查API测试成功')
  } catch (error) {
    console.error('健康检查API测试失败:', error)
    results.health = { error: error.message }
    ElMessage.error('健康检查API测试失败')
  } finally {
    loading.health = false
  }
}

// 测试登录API
const testLogin = async () => {
  loading.login = true
  try {
    const result = await authStore.login(loginForm)
    results.login = result
    if (result.success) {
      ElMessage.success('登录API测试成功')
    } else {
      ElMessage.error('登录API测试失败')
    }
  } catch (error) {
    console.error('登录API测试失败:', error)
    results.login = { error: error.message }
    ElMessage.error('登录API测试失败')
  } finally {
    loading.login = false
  }
}

// 测试获取用户列表API
const testGetUsers = async () => {
  loading.users = true
  try {
    const result = await usersAPI.getUsers()
    results.users = result
    ElMessage.success('用户列表API测试成功')
  } catch (error) {
    console.error('用户列表API测试失败:', error)
    results.users = { error: error.message }
    ElMessage.error('用户列表API测试失败')
  } finally {
    loading.users = false
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 14px;
}

.test-section {
  padding: 20px 0;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.result pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.config-info p {
  margin: 10px 0;
  color: #606266;
}

.config-info strong {
  color: #303133;
}
</style>
