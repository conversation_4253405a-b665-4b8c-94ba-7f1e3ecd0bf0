<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎使用主数据管理系统</p>
    </div>

    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon upload">
              <el-icon size="24"><Upload /></el-icon>
            </div>
            <div class="stat-info">
              <h3>数据加载</h3>
              <p class="stat-number">1,234</p>
              <p class="stat-desc">本月处理文件数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon match">
              <el-icon size="24"><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <h3>外部匹配</h3>
              <p class="stat-number">856</p>
              <p class="stat-desc">本月匹配记录数</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon size="24"><Check /></el-icon>
            </div>
            <div class="stat-info">
              <h3>成功率</h3>
              <p class="stat-number">98.5%</p>
              <p class="stat-desc">数据处理成功率</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon size="24"><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>活跃用户</h3>
              <p class="stat-number">42</p>
              <p class="stat-desc">本月活跃用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" size="large" @click="$router.push('/data-loader')">
              <el-icon><Upload /></el-icon>
              数据加载器
            </el-button>
            <el-button type="success" size="large" @click="$router.push('/external-match')">
              <el-icon><Connection /></el-icon>
              外部匹配
            </el-button>
            <el-button type="warning" size="large" @click="$router.push('/user-management')">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          <div class="recent-activities">
            <div class="activity-item">
              <el-icon class="activity-icon"><Upload /></el-icon>
              <div class="activity-content">
                <p class="activity-title">数据文件上传完成</p>
                <p class="activity-time">2分钟前</p>
              </div>
            </div>
            <div class="activity-item">
              <el-icon class="activity-icon"><Connection /></el-icon>
              <div class="activity-content">
                <p class="activity-title">外部匹配任务启动</p>
                <p class="activity-time">15分钟前</p>
              </div>
            </div>
            <div class="activity-item">
              <el-icon class="activity-icon"><Check /></el-icon>
              <div class="activity-content">
                <p class="activity-title">数据下发成功</p>
                <p class="activity-time">1小时前</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { Upload, Connection, Check, User } from '@element-plus/icons-vue'
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
}

.dashboard-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.upload {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.match {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.users {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.stat-number {
  margin: 0 0 4px 0;
  color: #409EFF;
  font-size: 24px;
  font-weight: bold;
}

.stat-desc {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.content-row {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions .el-button {
  justify-content: flex-start;
  height: 50px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  margin-right: 12px;
  color: #409EFF;
}

.activity-content {
  flex: 1;
}

.activity-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
}

.activity-time {
  margin: 0;
  color: #909399;
  font-size: 12px;
}
</style>
