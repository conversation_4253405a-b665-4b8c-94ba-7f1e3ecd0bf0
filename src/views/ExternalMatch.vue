<template>
  <div class="external-match">
    <div class="page-header">
      <h1>外部匹配</h1>
      <p>上传文件进行外部数据匹配处理</p>
    </div>

    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" class="steps">
      <el-step title="文件上传" description="选择并上传文件"></el-step>
      <el-step title="文件确认" description="确认文件信息"></el-step>
      <el-step title="处理进度" description="后台处理中"></el-step>
      <el-step title="结果审核" description="审核匹配结果"></el-step>
      <el-step title="数据下发" description="下发到关联系统"></el-step>
      <el-step title="完成" description="流程完成"></el-step>
    </el-steps>

    <!-- 步骤1: 文件上传 -->
    <el-card v-if="currentStep === 0" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Upload /></el-icon>
          <span>文件上传</span>
        </div>
      </template>
      
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        accept=".csv,.xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 CSV、Excel 格式文件，文件大小不超过 50MB
          </div>
        </template>
      </el-upload>

      <div class="step-actions">
        <el-button type="primary" :disabled="!selectedFile" @click="nextStep">
          下一步
        </el-button>
      </div>
    </el-card>

    <!-- 步骤2: 文件确认 -->
    <el-card v-if="currentStep === 1" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>文件信息确认</span>
        </div>
      </template>

      <div class="file-review">
        <h3>请确认以下文件信息：</h3>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">
            {{ selectedFile?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(selectedFile?.size) }}
          </el-descriptions-item>
          <el-descriptions-item label="文件类型">
            {{ getFileType(selectedFile?.name) }}
          </el-descriptions-item>
          <el-descriptions-item label="上传时间">
            {{ new Date().toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="文件位置" :span="2">
            /uploads/external-match/{{ selectedFile?.name }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="data-summary">
          <h4>数据摘要</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">预估记录数：</span>
                <span class="summary-value">{{ Math.floor(Math.random() * 10000) + 1000 }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">预估列数：</span>
                <span class="summary-value">{{ Math.floor(Math.random() * 20) + 5 }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <span class="summary-label">编码格式：</span>
                <span class="summary-value">UTF-8</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="startMatching">
          确认提交
        </el-button>
      </div>
    </el-card>

    <!-- 步骤3: 处理进度 -->
    <el-card v-if="currentStep === 2" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Loading /></el-icon>
          <span>后台处理进度</span>
        </div>
      </template>

      <div class="progress-content">
        <el-progress
          :percentage="Number(progress)"
          :status="progressStatus"
          :stroke-width="8"
          class="progress-bar"
        />
        <p class="progress-text">{{ progressText }}</p>
        
        <div class="processing-steps">
          <div class="processing-step" :class="{ active: progress >= 20 }">
            <el-icon><Check /></el-icon>
            <span>文件解析</span>
          </div>
          <div class="processing-step" :class="{ active: progress >= 40 }">
            <el-icon><Check /></el-icon>
            <span>数据清洗</span>
          </div>
          <div class="processing-step" :class="{ active: progress >= 60 }">
            <el-icon><Check /></el-icon>
            <span>外部匹配</span>
          </div>
          <div class="processing-step" :class="{ active: progress >= 80 }">
            <el-icon><Check /></el-icon>
            <span>结果生成</span>
          </div>
          <div class="processing-step" :class="{ active: progress >= 100 }">
            <el-icon><Check /></el-icon>
            <span>处理完成</span>
          </div>
        </div>

        <!-- 失败处理 -->
        <div v-if="processingFailed" class="failure-actions">
          <el-alert
            title="处理失败"
            type="error"
            description="文件处理过程中发生错误，请下载失败记录查看详情"
            show-icon
            :closable="false"
          />
          <div class="failure-buttons">
            <el-button type="danger" @click="downloadFailureReport">
              <el-icon><Download /></el-icon>
              下载失败记录
            </el-button>
            <el-button @click="resetProcess">
              返回重新开始
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 步骤4: 结果审核 -->
    <el-card v-if="currentStep === 3" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><View /></el-icon>
          <span>匹配结果审核</span>
        </div>
      </template>

      <div class="result-review">
        <el-alert
          title="匹配处理完成"
          type="success"
          description="请审核以下匹配结果信息，确认无误后可进行数据下发"
          show-icon
          :closable="false"
        />

        <div class="result-info">
          <h3>处理结果信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="处理批次ID">
              BATCH_{{ Date.now().toString().slice(-8) }}
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
              {{ new Date().toLocaleString() }}
            </el-descriptions-item>
            <el-descriptions-item label="匹配成功数">
              {{ matchResult.successCount }}
            </el-descriptions-item>
            <el-descriptions-item label="匹配失败数">
              {{ matchResult.failureCount }}
            </el-descriptions-item>
            <el-descriptions-item label="匹配成功率">
              {{ matchResult.successRate }}%
            </el-descriptions-item>
            <el-descriptions-item label="数据标识字段">
              {{ matchResult.dataId }}
            </el-descriptions-item>
            <el-descriptions-item label="结果文件位置" :span="2">
              /results/external-match/{{ matchResult.resultFile }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="result-actions-section">
          <h4>操作选项</h4>
          <el-button type="primary" @click="previewResults">
            <el-icon><View /></el-icon>
            预览结果数据
          </el-button>
          <el-button type="success" @click="downloadResults">
            <el-icon><Download /></el-icon>
            下载结果文件
          </el-button>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="resetProcess">重新开始</el-button>
        <el-button type="primary" @click="startDistribution">
          确认并开始数据下发
        </el-button>
      </div>
    </el-card>

    <!-- 步骤5: 数据下发 -->
    <el-card v-if="currentStep === 4" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Share /></el-icon>
          <span>数据下发进度</span>
        </div>
      </template>

      <div class="distribution-content">
        <h3>正在下发数据到关联系统</h3>

        <div class="distribution-systems">
          <div
            v-for="system in distributionSystems"
            :key="system.id"
            class="system-item"
            :class="{
              'processing': system.status === 'processing',
              'success': system.status === 'success',
              'failed': system.status === 'failed'
            }"
          >
            <div class="system-info">
              <h4>{{ system.name }}</h4>
              <p>{{ system.description }}</p>
            </div>
            <div class="system-status">
              <el-icon v-if="system.status === 'processing'" class="rotating">
                <Loading />
              </el-icon>
              <el-icon v-else-if="system.status === 'success'" class="success-icon">
                <CircleCheck />
              </el-icon>
              <el-icon v-else-if="system.status === 'failed'" class="error-icon">
                <CircleClose />
              </el-icon>
              <el-icon v-else class="pending-icon">
                <Clock />
              </el-icon>
              <span class="status-text">{{ getStatusText(system.status) }}</span>
            </div>
          </div>
        </div>

        <el-progress
          :percentage="Number(distributionProgress)"
          :status="distributionStatus"
          :stroke-width="8"
          class="distribution-progress"
        />
      </div>
    </el-card>

    <!-- 步骤6: 完成 -->
    <el-card v-if="currentStep === 5" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><CircleCheck /></el-icon>
          <span>流程完成</span>
        </div>
      </template>

      <el-result
        icon="success"
        title="外部匹配流程完成"
        sub-title="数据已成功下发到所有关联系统"
      >
        <template #extra>
          <div class="final-summary">
            <h4>处理摘要</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="summary-card">
                  <div class="summary-number">{{ matchResult.successCount }}</div>
                  <div class="summary-label">成功匹配</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-card">
                  <div class="summary-number">{{ distributionSystems.length }}</div>
                  <div class="summary-label">下发系统</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-card">
                  <div class="summary-number">{{ matchResult.successRate }}%</div>
                  <div class="summary-label">成功率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="final-actions">
            <el-button type="primary" @click="resetProcess">
              处理新文件
            </el-button>
            <el-button @click="$router.push('/')">
              返回首页
            </el-button>
          </div>
        </template>
      </el-result>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useExternalMatchStore } from '@/stores/externalMatch'
import {
  Upload,
  UploadFilled,
  Document,
  Loading,
  Check,
  Download,
  View,
  Share,
  CircleCheck,
  CircleClose,
  Clock
} from '@element-plus/icons-vue'

const externalMatchStore = useExternalMatchStore()
const currentStep = ref(0)
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)
const currentTaskId = ref(null)
const progress = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const processingFailed = ref(false)
const distributionProgress = ref(0)
const distributionStatus = ref('')

const matchResult = reactive({
  successCount: 8456,
  failureCount: 234,
  successRate: 97.3,
  dataId: 'MDM_MATCH_' + Date.now().toString().slice(-6),
  resultFile: 'match_result_' + new Date().toISOString().split('T')[0] + '.xlsx'
})

const distributionSystems = ref([
  { id: 1, name: 'CRM系统', description: '客户关系管理系统', status: 'pending' },
  { id: 2, name: 'ERP系统', description: '企业资源规划系统', status: 'pending' },
  { id: 3, name: '数据仓库', description: '企业数据仓库', status: 'pending' },
  { id: 4, name: 'BI系统', description: '商业智能系统', status: 'pending' }
])

const handleFileChange = (file) => {
  selectedFile.value = file.raw
  fileList.value = [file]
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

const getFileType = (filename) => {
  if (!filename) return ''
  const ext = filename.split('.').pop().toLowerCase()
  const types = {
    'csv': 'CSV文件',
    'xlsx': 'Excel文件',
    'xls': 'Excel文件'
  }
  return types[ext] || '未知类型'
}

const nextStep = async () => {
  if (currentStep.value === 0) {
    // 第一步：上传文件
    if (!selectedFile.value) {
      ElMessage.error('请选择文件')
      return
    }

    try {
      const result = await externalMatchStore.uploadFile(selectedFile.value)
      if (result.success) {
        // 保存任务ID用于后续操作
        currentTaskId.value = result.task.id
        currentStep.value++
      }
    } catch (error) {
      console.error('文件上传失败:', error)
    }
  } else if (currentStep.value < 5) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const startMatching = async () => {
  if (!currentTaskId.value) {
    ElMessage.error('任务ID不存在')
    return
  }

  try {
    const result = await externalMatchStore.confirmAndStartMatching(currentTaskId.value)

    if (result && result.success) {
      currentStep.value = 2
      progressStatus.value = ''
      progressText.value = '开始匹配处理...'
      processingFailed.value = false

      // 立即获取一次进度
      try {
        const initialProgress = await externalMatchStore.getMatchProgress(currentTaskId.value)
        if (initialProgress) {
          updateMatchProgress(initialProgress)
        }
      } catch (error) {
        console.error('获取初始进度失败:', error)
      }

      // 启动轮询
      externalMatchStore.pollTaskStatus(currentTaskId.value, (taskProgress) => {
        updateMatchProgress(taskProgress)
      })
    } else {
      ElMessage.error('确认匹配失败')
    }
  } catch (error) {
    console.error('开始匹配失败:', error)
    ElMessage.error('开始匹配失败: ' + error.message)
  }
}

// 根据任务进度更新匹配进度
const updateMatchProgress = (taskProgress) => {
  // 优先使用API返回的进度值，如果没有则根据状态计算
  if (typeof taskProgress.progress === 'number') {
    progress.value = taskProgress.progress
  }

  // 根据状态设置进度文本和其他属性
  switch (taskProgress.status) {
    case 'UPLOADED':
    case 'PENDING':
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 5
      }
      progressText.value = '任务已创建，等待处理...'
      break
    case 'REVIEWING':
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 20
      }
      progressText.value = '正在审核文件...'
      break
    case 'PROCESSING':
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 60
      }
      progressText.value = '正在进行外部匹配...'
      break
    case 'MATCHED':
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 100
      }
      progressStatus.value = 'success'
      progressText.value = '匹配完成！'
      // 更新匹配结果
      if (taskProgress.result) {
        matchResult.successCount = taskProgress.result.success_count || 0
        matchResult.failureCount = taskProgress.result.failure_count || 0
        matchResult.successRate = taskProgress.result.success_rate || 0
      }
      setTimeout(() => {
        currentStep.value = 3
      }, 1000)
      break
    case 'FAILED':
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 100
      }
      progressStatus.value = 'exception'
      progressText.value = '匹配失败'
      processingFailed.value = true
      break
    default:
      if (typeof taskProgress.progress !== 'number') {
        progress.value = 5
      }
      progressText.value = `状态: ${taskProgress.status}`
      break
  }
}

const downloadFailureReport = async () => {
  if (!currentTaskId.value) {
    ElMessage.error('任务ID不存在')
    return
  }

  try {
    await externalMatchStore.downloadErrorRecords(currentTaskId.value)
  } catch (error) {
    console.error('下载失败记录失败:', error)
  }
}

const previewResults = () => {
  ElMessage.info('结果预览功能开发中...')
}

const downloadResults = () => {
  ElMessage.success('结果文件下载中...')
  // 这里应该实现真实的文件下载逻辑
}

const startDistribution = async () => {
  if (!currentTaskId.value) {
    ElMessage.error('任务ID不存在')
    return
  }

  try {
    const result = await externalMatchStore.startDistribution(currentTaskId.value)

    if (result && result.success) {
      currentStep.value = 4
      distributionProgress.value = 0
      distributionStatus.value = ''

      // 开始轮询分发进度
      pollDistributionProgress()
    } else {
      ElMessage.error('分发启动失败')
    }
  } catch (error) {
    console.error('开始分发失败:', error)
    ElMessage.error('开始分发失败: ' + error.message)
  }
}

// 轮询分发进度
const pollDistributionProgress = async () => {
  const poll = async () => {
    try {
      const progress = await externalMatchStore.getDistributionProgress(currentTaskId.value)

      if (progress) {
        distributionProgress.value = progress.overall_progress || progress.progress || 0

        // 更新各系统状态
        if (progress.nodes || progress.systems) {
          const systems = progress.nodes || progress.systems
          systems.forEach((systemProgress, index) => {
            if (distributionSystems.value[index]) {
              distributionSystems.value[index].status = systemProgress.status === 'SUCCESS' ? 'success' :
                               systemProgress.status === 'PROCESSING' ? 'processing' : 'pending'
            }
          })
        }

        const overallStatus = progress.overall_status || progress.status

        if (overallStatus === 'COMPLETED') {
          distributionStatus.value = 'success'
          setTimeout(() => {
            currentStep.value = 5
          }, 1000)
        } else if (overallStatus === 'DISTRIBUTING') {
          setTimeout(poll, 2000) // 继续轮询
        }
      }
    } catch (error) {
      console.error('获取分发进度失败:', error)
    }
  }

  poll()
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '等待中',
    'processing': '处理中',
    'success': '成功',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

const resetProcess = () => {
  currentStep.value = 0
  fileList.value = []
  selectedFile.value = null
  currentTaskId.value = null
  progress.value = 0
  progressStatus.value = ''
  progressText.value = ''
  processingFailed.value = false
  distributionProgress.value = 0
  distributionStatus.value = ''

  // 重置分发系统状态
  distributionSystems.value.forEach(system => {
    system.status = 'pending'
  })
}
</script>

<style scoped>
.external-match {
  max-width: 900px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.steps {
  margin-bottom: 30px;
}

.step-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.upload-demo {
  margin-bottom: 20px;
}

.file-review h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.data-summary {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.data-summary h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-label {
  color: #606266;
}

.summary-value {
  color: #303133;
  font-weight: bold;
}

.step-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.progress-content {
  text-align: center;
}

.progress-bar {
  margin-bottom: 20px;
}

.progress-text {
  margin-bottom: 30px;
  color: #606266;
  font-size: 16px;
}

.processing-steps {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.processing-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #c0c4cc;
  transition: color 0.3s;
}

.processing-step.active {
  color: #67c23a;
}

.failure-actions {
  margin-top: 30px;
}

.failure-buttons {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.result-review {
  margin-bottom: 20px;
}

.result-info {
  margin: 20px 0;
}

.result-info h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.result-actions-section {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-actions-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.distribution-content h3 {
  margin: 0 0 20px 0;
  color: #303133;
  text-align: center;
}

.distribution-systems {
  margin-bottom: 30px;
}

.system-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s;
}

.system-item.processing {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.system-item.success {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.system-item.failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.system-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.system-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 14px;
  font-weight: bold;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.pending-icon {
  color: #c0c4cc;
}

.distribution-progress {
  margin-top: 20px;
}

.final-summary {
  margin-bottom: 30px;
}

.final-summary h4 {
  margin: 0 0 20px 0;
  color: #303133;
  text-align: center;
}

.summary-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.summary-label {
  color: #606266;
  font-size: 14px;
}

.final-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
