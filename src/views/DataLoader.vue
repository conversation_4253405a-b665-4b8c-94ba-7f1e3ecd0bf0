<template>
  <div class="data-loader">
    <div class="page-header">
      <h1>数据加载器</h1>
      <p>上传数据文件并选择操作类型进行处理</p>
    </div>

    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" class="steps">
      <el-step title="文件上传" description="选择并上传数据文件"></el-step>
      <el-step title="选择类型" description="选择操作类型"></el-step>
      <el-step title="数据校验" description="验证数据格式和内容"></el-step>
      <el-step title="数据处理" description="数据处理中"></el-step>
      <el-step title="完成" description="处理结果"></el-step>
    </el-steps>

    <!-- 步骤1: 文件上传 -->
    <el-card v-if="currentStep === 0" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Upload /></el-icon>
          <span>文件上传</span>
        </div>
      </template>
      
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="fileList"
        accept=".csv,.xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持Excel 格式文件
          </div>
        </template>
      </el-upload>

      <div class="step-actions">
        <el-button type="primary" :disabled="!selectedFile" @click="nextStep">
          下一步
        </el-button>
      </div>
    </el-card>

    <!-- 步骤2: 选择操作类型 -->
    <el-card v-if="currentStep === 1" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>选择操作类型</span>
        </div>
      </template>

      <div class="file-info">
        <h3>已选择文件：{{ selectedFile?.name }}</h3>
        <p>文件大小：{{ formatFileSize(selectedFile?.size) }}</p>
      </div>

      <el-form :model="form" label-width="120px" class="entity-form">
        <el-form-item label="实体类型" required>
          <el-radio-group v-model="form.entityType" size="large">
            <el-radio value="product">
              <div class="radio-option">
                <h4>创建</h4>
<!--                <p>产品主数据信息</p>-->
              </div>
            </el-radio>
            <el-radio value="hco">
              <div class="radio-option">
                <h4>更新</h4>
<!--                <p>医疗机构主数据信息</p>-->
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" :disabled="!form.entityType" @click="startValidation">
          开始校验
        </el-button>
      </div>
    </el-card>

    <!-- 步骤3: 数据校验 -->
    <el-card v-if="currentStep === 2" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><DocumentChecked /></el-icon>
          <span>数据校验</span>
        </div>
      </template>

      <!-- 校验中状态 -->
      <div v-if="dataLoaderStore.validating" class="validation-content">
        <div class="loading-spinner">
          <el-icon class="rotating"><Loading /></el-icon>
        </div>
        <p class="validation-text">正在校验数据，请稍候...</p>
      </div>

      <!-- 校验成功状态 -->
      <div v-else-if="dataLoaderStore.validationResult?.success" class="validation-success">
        <el-result
          icon="success"
          title="数据校验成功"
          sub-title="数据格式和内容验证通过，可以进行下一步处理"
        >
          <template #extra>
            <div class="validation-actions">
              <el-button @click="prevStep">上一步</el-button>
              <el-button type="primary" @click="startProcessing">
                确认并开始处理
              </el-button>
            </div>
          </template>
        </el-result>
      </div>

      <!-- 校验失败状态 -->
      <div v-else-if="dataLoaderStore.validationResult && !dataLoaderStore.validationResult.success" class="validation-failed">
        <el-result
          icon="error"
          title="数据校验失败"
          :sub-title="dataLoaderStore.validationResult.error || '数据格式或内容存在问题'"
        >
          <template #extra>
            <div class="validation-actions">
              <el-button
                v-if="dataLoaderStore.validationResult.validation_id"
                type="warning"
                @click="downloadErrorReport"
              >
                下载失败报告
              </el-button>
              <el-button type="primary" @click="resetToUpload">
                重新上传
              </el-button>
            </div>
          </template>
        </el-result>
      </div>
    </el-card>

    <!-- 步骤4: 处理进度 -->
    <el-card v-if="currentStep === 3" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><Loading /></el-icon>
          <span>数据处理中</span>
        </div>
      </template>

      <div class="processing-content">
        <div class="loading-spinner">
          <el-icon class="rotating"><Loading /></el-icon>
        </div>
        <p class="processing-text">处理中...</p>
      </div>
    </el-card>

    <!-- 步骤5: 完成 -->
    <el-card v-if="currentStep === 4" class="step-card">
      <template #header>
        <div class="card-header">
          <el-icon><CircleCheck /></el-icon>
          <span>处理完成</span>
        </div>
      </template>

      <el-result
        :icon="processingResult.success ? 'success' : 'error'"
        :title="processingResult.title"
        :sub-title="processingResult.message"
      >
        <template #extra>
          <div class="result-actions">
            <el-button type="primary" @click="resetProcess">
              处理新文件
            </el-button>
            <el-button @click="$router.push('/')">
              返回首页
            </el-button>
          </div>
        </template>
      </el-result>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useDataLoaderStore } from '@/stores/dataLoader'
import {
  Upload,
  UploadFilled,
  Setting,
  Loading,
  Check,
  CircleCheck,
  DocumentChecked
} from '@element-plus/icons-vue'

const dataLoaderStore = useDataLoaderStore()
const currentStep = ref(0)
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)

const form = reactive({
  entityType: ''
})

const processingResult = reactive({
  success: true,
  title: '',
  message: ''
})

const handleFileChange = (file) => {
  selectedFile.value = file.raw
  fileList.value = [file]
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 开始数据校验
const startValidation = async () => {
  if (!selectedFile.value || !form.entityType) {
    ElMessage.error('请选择文件和实体类型')
    return
  }

  currentStep.value = 2

  try {
    await dataLoaderStore.validateFile(selectedFile.value, form.entityType)
  } catch (error) {
    console.error('校验失败:', error)
  }
}

// 下载错误报告
const downloadErrorReport = async () => {
  if (dataLoaderStore.validationResult?.validation_id) {
    await dataLoaderStore.downloadValidationErrors(dataLoaderStore.validationResult.validation_id)
  }
}

// 重新上传（返回第一步）
const resetToUpload = () => {
  currentStep.value = 0
  fileList.value = []
  selectedFile.value = null
  form.entityType = ''
  dataLoaderStore.validationResult = null
}

const startProcessing = async () => {
  if (!selectedFile.value || !form.entityType) {
    ElMessage.error('请选择文件和实体类型')
    return
  }

  currentStep.value = 3

  try {
    const result = await dataLoaderStore.uploadFile(selectedFile.value, form.entityType)

    if (result.success) {
      // 开始轮询任务状态
      dataLoaderStore.pollTaskStatus(result.task.id, (task) => {
        updateProgressFromTask(task)
      })
    } else {
      processingResult.success = false
      processingResult.title = '数据处理失败'
      processingResult.message = result.error || '文件上传失败'
      setTimeout(() => {
        currentStep.value = 3
      }, 1000)
    }
  } catch (error) {
    console.error('处理失败:', error)
    processingResult.success = false
    processingResult.title = '数据处理失败'
    processingResult.message = '处理过程中发生错误'
    setTimeout(() => {
      currentStep.value = 3
    }, 1000)
  }
}

// 根据任务状态更新进度
const updateProgressFromTask = (task) => {
  switch (task.status) {
    case 'pending':
    case 'processing':
      // 保持在处理中状态，显示转圈动画
      break
    case 'success':
      processingResult.success = true
      processingResult.title = '数据处理成功'
      processingResult.message = `成功处理 ${form.entityType === 'product' ? '创建' : '更新'} 数据文件`
      setTimeout(() => {
        currentStep.value = 4
      }, 1000)
      break
    case 'failed':
      processingResult.success = false
      processingResult.title = '数据处理失败'
      processingResult.message = task.error_message || '数据处理过程中发生错误'
      setTimeout(() => {
        currentStep.value = 4
      }, 1000)
      break
  }
}

const resetProcess = () => {
  currentStep.value = 0
  fileList.value = []
  selectedFile.value = null
  form.entityType = ''
}
</script>

<style scoped>
.data-loader {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.steps {
  margin-bottom: 30px;
}

.step-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.upload-demo {
  margin-bottom: 20px;
}

.file-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.file-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.file-info p {
  margin: 0;
  color: #909399;
}

.entity-form {
  margin-bottom: 20px;
}

.radio-option h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.radio-option p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.step-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.processing-content {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  margin-bottom: 20px;
}

.loading-spinner .rotating {
  font-size: 48px;
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.processing-text {
  margin: 0;
  color: #606266;
  font-size: 18px;
  font-weight: 500;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.validation-content {
  text-align: center;
  padding: 60px 20px;
}

.validation-text {
  margin: 20px 0 0 0;
  color: #606266;
  font-size: 18px;
  font-weight: 500;
}

.validation-success,
.validation-failed {
  padding: 20px 0;
}

.validation-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
