#!/usr/bin/env python3
"""
MDM 数据库管理工具
集成了数据库创建、初始化、测试和管理功能
"""
import asyncio
import argparse
import sys
import pymysql
from app.core.config import settings
from app.core.database import init_db, check_db_connection, check_tables_exist
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MySQL 方言标志 - 用于 IDE SQL 检查
MYSQL_DIALECT = True


def create_database():
    """创建MySQL数据库"""
    try:
        # 解析数据库URL
        # mysql+aiomysql://root:123456@localhost:3306/mdm_backend
        url_parts = settings.database_url.replace("mysql+aiomysql://", "").split("/")
        db_name = url_parts[1]
        
        auth_host = url_parts[0].split("@")
        host_port = auth_host[1].split(":")
        host = host_port[0]
        port = int(host_port[1])
        
        user_pass = auth_host[0].split(":")
        user = user_pass[0]
        password = user_pass[1]
        
        logger.info(f"连接到MySQL服务器: {host}:{port}")
        logger.info(f"用户: {user}")
        logger.info(f"数据库: {db_name}")
        
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            # noinspection SqlDialectInspection,SqlNoDataSourceInspection
            show_db_query = "SHOW DATABASES LIKE %s"
            cursor.execute(show_db_query, (db_name,))
            result = cursor.fetchone()

            if result:
                logger.info(f"数据库 '{db_name}' 已存在")
            else:
                # 创建数据库
                # noinspection SqlDialectInspection,SqlNoDataSourceInspection
                create_db_query = f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
                cursor.execute(create_db_query)
                logger.info(f"数据库 '{db_name}' 创建成功")
            
            # 提交更改
            connection.commit()
            
        connection.close()
        logger.info("数据库创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        return False


async def create_default_users():
    """创建默认用户"""
    from app.core.database import AsyncSessionLocal
    from app.models.database import User
    from app.models.user import UserRole
    from app.core.security import get_password_hash
    from sqlalchemy import select
    
    async with AsyncSessionLocal() as session:
        try:
            # 检查是否已有管理员用户
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                # 创建管理员用户
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="系统管理员",
                    hashed_password=get_password_hash("admin"),
                    role=UserRole.ADMIN,
                    is_active=True
                )
                session.add(admin_user)
                logger.info("创建管理员用户: admin/admin")
            
            # 检查是否已有普通用户
            result = await session.execute(
                select(User).where(User.username == "user")
            )
            normal_user = result.scalar_one_or_none()
            
            if not normal_user:
                # 创建普通用户
                normal_user = User(
                    username="user",
                    email="<EMAIL>",
                    full_name="普通用户",
                    hashed_password=get_password_hash("secret"),
                    role=UserRole.USER,
                    is_active=True
                )
                session.add(normal_user)
                logger.info("创建普通用户: user/secret")
            
            await session.commit()
            
        except Exception as e:
            await session.rollback()
            logger.error(f"创建默认用户失败: {e}")
            raise


async def init_database(force: bool = False):
    """初始化数据库"""
    print("=== 数据库初始化工具 ===")
    print(f"数据库URL: {settings.database_url}")
    print()
    
    # 检查数据库连接
    print("📊 检查数据库连接...")
    if not await check_db_connection():
        print("❌ 数据库连接失败，请检查配置")
        return False
    
    print("✅ 数据库连接成功")
    
    # 检查表是否存在
    if not force:
        print("🔍 检查数据库表...")
        if await check_tables_exist():
            print("⚠️  数据库表已存在")
            response = input("是否要重新创建所有表？这将删除所有数据！(y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return False
            force = True
    
    # 初始化数据库
    print("🔧 初始化数据库表...")
    try:
        await init_db(force=force)
        print("✅ 数据库表初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False


async def check_database():
    """检查数据库状态"""
    print("=== 数据库状态检查 ===")
    print(f"数据库URL: {settings.database_url}")
    print()
    
    # 检查连接
    print("📊 检查数据库连接...")
    if await check_db_connection():
        print("✅ 数据库连接正常")
    else:
        print("❌ 数据库连接失败")
        return
    
    # 检查表
    print("🔍 检查数据库表...")
    if await check_tables_exist():
        print("✅ 数据库表已存在")
        
        # 显示表信息
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as session:
            # 获取表列表
            # noinspection SqlDialectInspection,SqlNoDataSourceInspection
            result = await session.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"📋 数据库表 ({len(tables)} 个):")
            for table in tables:
                print(f"  - {table[0]}")
                
            # 检查用户数量
            from app.models.database import User
            from sqlalchemy import select, func
            result = await session.execute(select(func.count(User.id)))
            user_count = result.scalar()
            print(f"👥 用户数量: {user_count}")
            
    else:
        print("❌ 数据库表不存在，需要初始化")


async def test_database():
    """测试数据库连接和功能"""
    print("=== 数据库连接测试 ===")
    print(f"数据库URL: {settings.database_url}")
    print()
    
    # 测试基本连接
    print("🔍 测试基本数据库连接...")
    if not await check_db_connection():
        print("❌ 基本连接测试失败，请检查:")
        print("1. MySQL服务是否启动")
        print("2. 数据库连接参数是否正确")
        print("3. 数据库是否已创建")
        return False
    
    print("✅ 数据库连接成功")
    print()
    
    # 测试数据库信息
    print("🔍 获取数据库信息...")
    try:
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as session:
            # 获取数据库版本
            result = await session.execute(text("SELECT VERSION()"))
            version = result.scalar()
            print(f"✅ MySQL版本: {version}")
            
            # 获取当前数据库名
            result = await session.execute(text("SELECT DATABASE()"))
            db_name = result.scalar()
            print(f"✅ 当前数据库: {db_name}")
            
            # 获取表列表
            result = await session.execute(text("SHOW TABLES"))
            tables = result.fetchall()
            print(f"✅ 数据库表 ({len(tables)} 个):")
            for table in tables:
                print(f"  - {table[0]}")
            
    except Exception as e:
        print(f"❌ 获取数据库信息失败: {e}")
        return False
    
    print()
    
    # 测试表操作
    print("🔍 测试数据库表操作...")
    try:
        from app.core.database import AsyncSessionLocal
        from app.models.database import User
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as session:
            # 测试查询用户表
            result = await session.execute(select(User))
            users = result.scalars().all()
            print(f"✅ 用户表查询成功，共有 {len(users)} 个用户")
            
            # 显示用户信息
            for user in users:
                print(f"  - 用户: {user.username} ({user.role.value}) - {user.full_name}")
            
    except Exception as e:
        print(f"❌ 数据库表操作失败: {e}")
        return False
    
    print("\n🎉 所有数据库测试通过！")
    return True


async def setup_database():
    """完整的数据库设置流程"""
    print("=== 完整数据库设置 ===")
    print(f"数据库URL: {settings.database_url}")
    print()

    # 1. 创建数据库
    print("📊 创建数据库...")
    if not create_database():
        print("❌ 数据库创建失败")
        return False
    print("✅ 数据库创建成功")

    # 2. 初始化表
    print("🔧 初始化数据库表...")
    try:
        await init_db()
        print("✅ 数据库表初始化完成")
    except Exception as e:
        print(f"❌ 数据库表初始化失败: {e}")
        return False

    # 3. 创建默认用户
    print("👤 创建默认用户...")
    try:
        await create_default_users()
        print("✅ 默认用户创建完成")
    except Exception as e:
        print(f"❌ 默认用户创建失败: {e}")
        return False

    print("\n🎉 数据库设置完成！")
    return True


async def create_admin_user():
    """创建管理员用户（交互式）"""
    from app.core.database import AsyncSessionLocal
    from app.models.database import User
    from app.models.user import UserRole
    from app.core.security import get_password_hash
    from sqlalchemy import select
    import getpass

    print("=== 创建管理员用户 ===")

    username = input("请输入用户名: ").strip()
    if not username:
        print("❌ 用户名不能为空")
        return

    email = input("请输入邮箱: ").strip()
    if not email:
        print("❌ 邮箱不能为空")
        return

    full_name = input("请输入全名: ").strip()
    if not full_name:
        full_name = username

    password = getpass.getpass("请输入密码: ")
    if not password:
        print("❌ 密码不能为空")
        return

    async with AsyncSessionLocal() as session:
        try:
            # 检查用户名是否已存在
            result = await session.execute(
                select(User).where(User.username == username)
            )
            existing_user = result.scalar_one_or_none()

            if existing_user:
                print(f"❌ 用户名 '{username}' 已存在")
                return

            # 创建新用户
            new_user = User(
                username=username,
                email=email,
                full_name=full_name,
                hashed_password=get_password_hash(password),
                role=UserRole.ADMIN,
                is_active=True
            )

            session.add(new_user)
            await session.commit()

            print(f"✅ 管理员用户 '{username}' 创建成功")

        except Exception as e:
            await session.rollback()
            print(f"❌ 创建用户失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MDM数据库管理工具",
        epilog="""
示例用法:
  python manage_db.py setup              # 完整设置（创建数据库+初始化+默认用户）
  python manage_db.py create-db          # 仅创建数据库
  python manage_db.py init               # 初始化表结构
  python manage_db.py check              # 检查数据库状态
  python manage_db.py test               # 测试数据库连接
  python manage_db.py create-admin       # 创建管理员用户
  python manage_db.py create-defaults    # 创建默认用户
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 完整设置命令
    subparsers.add_parser("setup", help="完整数据库设置（创建数据库+初始化+默认用户）")

    # 创建数据库命令
    subparsers.add_parser("create-db", help="创建MySQL数据库")

    # 初始化命令
    init_parser = subparsers.add_parser("init", help="初始化数据库表结构")
    init_parser.add_argument("--force", action="store_true", help="强制重新创建表")

    # 检查命令
    subparsers.add_parser("check", help="检查数据库状态")

    # 测试命令
    subparsers.add_parser("test", help="测试数据库连接和功能")

    # 创建管理员用户命令
    subparsers.add_parser("create-admin", help="创建管理员用户")

    # 创建默认用户命令
    subparsers.add_parser("create-defaults", help="创建默认用户（admin/admin, user/secret）")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        if args.command == "setup":
            success = await setup_database()
            sys.exit(0 if success else 1)
        elif args.command == "create-db":
            success = create_database()
            sys.exit(0 if success else 1)
        elif args.command == "init":
            success = await init_database(force=args.force)
            sys.exit(0 if success else 1)
        elif args.command == "check":
            await check_database()
        elif args.command == "test":
            success = await test_database()
            sys.exit(0 if success else 1)
        elif args.command == "create-admin":
            await create_admin_user()
        elif args.command == "create-defaults":
            await create_default_users()
    except KeyboardInterrupt:
        print("\n❌ 操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
