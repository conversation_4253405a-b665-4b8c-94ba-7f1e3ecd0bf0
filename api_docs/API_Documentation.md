# MDM Backend API


    ## 主数据管理后台服务

    这是一个完整的MDM（主数据管理）后台API服务，提供以下功能：

    ### 🔐 认证功能
    - 用户登录/登出
    - JWT令牌认证
    - 用户权限管理

    ### 👥 用户管理
    - 用户CRUD操作
    - 角色管理（管理员/普通用户/查看者）
    - 用户信息维护

    ### 📁 数据加载器
    - 支持CSV/Excel文件上传
    - 实体类型选择（product/hco）
    - 实时处理进度跟踪
    - 处理结果统计

    ### 🔄 外部匹配
    - 文件上传和数据审核
    - 智能数据匹配
    - 多系统数据分发
    - 失败记录下载

    ### 📊 进度监控
    - 实时任务状态跟踪
    - 详细进度信息
    - 错误处理和报告

    ---

    **技术栈**: FastAPI + Python 3.10 + JWT + Pydantic

    **认证方式**: Bearer Token (JWT)

    **默认账号**:
    - 管理员: admin/secret
    - 普通用户: user/secret
    

**版本**: 1.0.0

## 服务器地址

- **开发环境**: http://localhost:8000
- **生产环境**: https://api.mdm.com

## 认证方式

使用Bearer Token认证，在请求头中添加：
```
Authorization: Bearer <your_jwt_token>
```

## API端点

### 认证

#### POST /api/v1/auth/login

**用户登录**

用户登录接口

- **username**: 用户名
- **password**: 密码

**请求体**: JSON格式

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### POST /api/v1/auth/logout

**用户登出**

用户登出接口

注意：由于使用JWT，实际的登出需要在客户端删除token

**响应**:

- `200`: Successful Response

---

#### GET /api/v1/auth/me

**获取当前用户信息**

获取当前登录用户的信息

**响应**:

- `200`: Successful Response

---

#### POST /api/v1/auth/refresh

**刷新令牌**

刷新访问令牌

**响应**:

- `200`: Successful Response

---

### 用户管理

#### GET /api/v1/users/

**获取用户列表**

获取用户列表（仅管理员）

- **skip**: 跳过的记录数
- **limit**: 返回的记录数限制

**请求参数**:

- `skip` (query) - 可选: 
- `limit` (query) - 可选: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### POST /api/v1/users/

**创建用户**

创建新用户（仅管理员）

**请求体**: JSON格式

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/users/{user_id}

**获取用户详情**

获取指定用户的详情

- 管理员可以查看所有用户
- 普通用户只能查看自己的信息

**请求参数**:

- `user_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### PUT /api/v1/users/{user_id}

**更新用户**

更新用户信息

- 管理员可以更新所有用户
- 普通用户只能更新自己的部分信息

**请求参数**:

- `user_id` (path) - 必需: 

**请求体**: JSON格式

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### DELETE /api/v1/users/{user_id}

**删除用户**

删除用户（仅管理员）

**请求参数**:

- `user_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

### 数据加载器

#### POST /api/v1/data-loader/upload

**上传文件并创建数据加载任务**

上传文件并创建数据加载任务

- **file**: 上传的文件
- **entity_type**: 实体类型（product 或 hco）

**请求体**: JSON格式

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/data-loader/tasks

**获取数据加载任务列表**

获取数据加载任务列表

**请求参数**:

- `skip` (query) - 可选: 
- `limit` (query) - 可选: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/data-loader/tasks/{task_id}

**获取任务详情**

获取指定任务的详情

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/data-loader/tasks/{task_id}/progress

**获取任务进度**

获取任务处理进度

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/data-loader/tasks/{task_id}/result

**获取任务结果**

获取任务处理结果

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

### 外部匹配

#### POST /api/v1/external-match/upload

**上传文件创建外部匹配任务**

上传文件并创建外部匹配任务

- **file**: 上传的文件

**请求体**: JSON格式

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/external-match/tasks/{task_id}/review

**获取文件审核信息**

获取文件审核信息

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### POST /api/v1/external-match/tasks/{task_id}/confirm

**确认文件并开始匹配**

确认文件信息并开始匹配处理

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/external-match/tasks/{task_id}/progress

**获取匹配进度**

获取匹配处理进度

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/external-match/tasks/{task_id}/result

**获取匹配结果**

获取匹配结果信息

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### POST /api/v1/external-match/tasks/{task_id}/distribute

**开始数据分发**

开始数据分发到各关联系统

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/external-match/tasks/{task_id}/distribution

**获取分发进度**

获取数据分发进度

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /api/v1/external-match/tasks/{task_id}/download-errors

**下载失败记录**

下载失败记录文件

**请求参数**:

- `task_id` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

### 其他

#### GET /

**获取系统信息**

获取MDM系统基本信息

**响应**:

- `200`: Successful Response

---

#### GET /hello/{name}

**个性化问候**

个性化问候端点

**请求参数**:

- `name` (path) - 必需: 

**响应**:

- `200`: Successful Response
- `422`: Validation Error

---

#### GET /health

**健康检查**

系统健康检查端点

**响应**:

- `200`: Successful Response

---

