{"info": {"name": "MDM Backend API", "description": "\n    ## 主数据管理后台服务\n\n    这是一个完整的MDM（主数据管理）后台API服务，提供以下功能：\n\n    ### 🔐 认证功能\n    - 用户登录/登出\n    - JWT令牌认证\n    - 用户权限管理\n\n    ### 👥 用户管理\n    - 用户CRUD操作\n    - 角色管理（管理员/普通用户/查看者）\n    - 用户信息维护\n\n    ### 📁 数据加载器\n    - 支持CSV/Excel文件上传\n    - 实体类型选择（product/hco）\n    - 实时处理进度跟踪\n    - 处理结果统计\n\n    ### 🔄 外部匹配\n    - 文件上传和数据审核\n    - 智能数据匹配\n    - 多系统数据分发\n    - 失败记录下载\n\n    ### 📊 进度监控\n    - 实时任务状态跟踪\n    - 详细进度信息\n    - 错误处理和报告\n\n    ---\n\n    **技术栈**: FastAPI + Python 3.10 + JWT + Pydantic\n\n    **认证方式**: Bearer Token (JWT)\n\n    **默认账号**:\n    - 管理员: admin/secret\n    - 普通用户: user/secret\n    ", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "认证", "item": [{"name": "用户登录", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "用户登出", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}}}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/me", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "me"]}}}, {"name": "刷新令牌", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}}}]}, {"name": "用户管理", "item": [{"name": "获取用户列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/", "host": ["{{base_url}}"], "path": ["api", "v1", "users"]}}}, {"name": "创建用户", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/", "host": ["{{base_url}}"], "path": ["api", "v1", "users"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "获取用户详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/{user_id}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "{user_id}"]}}}, {"name": "更新用户", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/{user_id}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "{user_id}"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "删除用户", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/{user_id}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "{user_id}"]}}}]}, {"name": "数据加载器", "item": [{"name": "上传文件并创建数据加载任务", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/data-loader/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "data-loader", "upload"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "获取数据加载任务列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/data-loader/tasks", "host": ["{{base_url}}"], "path": ["api", "v1", "data-loader", "tasks"]}}}, {"name": "获取任务详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/data-loader/tasks/{task_id}", "host": ["{{base_url}}"], "path": ["api", "v1", "data-loader", "tasks", "{task_id}"]}}}, {"name": "获取任务进度", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/data-loader/tasks/{task_id}/progress", "host": ["{{base_url}}"], "path": ["api", "v1", "data-loader", "tasks", "{task_id}", "progress"]}}}, {"name": "获取任务结果", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/data-loader/tasks/{task_id}/result", "host": ["{{base_url}}"], "path": ["api", "v1", "data-loader", "tasks", "{task_id}", "result"]}}}]}, {"name": "外部匹配", "item": [{"name": "上传文件创建外部匹配任务", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "upload"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "获取文件审核信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/review", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "review"]}}}, {"name": "确认文件并开始匹配", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/confirm", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "confirm"]}}}, {"name": "获取匹配进度", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/progress", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "progress"]}}}, {"name": "获取匹配结果", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/result", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "result"]}}}, {"name": "开始数据分发", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/distribute", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "distribute"]}}}, {"name": "获取分发进度", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/distribution", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "distribution"]}}}, {"name": "下载失败记录", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/external-match/tasks/{task_id}/download-errors", "host": ["{{base_url}}"], "path": ["api", "v1", "external-match", "tasks", "{task_id}", "download-errors"]}}}]}, {"name": "其他", "item": [{"name": "获取系统信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}}, {"name": "个性化问候", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/hello/{name}", "host": ["{{base_url}}"], "path": ["hello", "{name}"]}}}, {"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}]}]}