{"openapi": "3.1.0", "info": {"title": "MDM Backend API", "description": "\n    ## 主数据管理后台服务\n\n    这是一个完整的MDM（主数据管理）后台API服务，提供以下功能：\n\n    ### 🔐 认证功能\n    - 用户登录/登出\n    - JWT令牌认证\n    - 用户权限管理\n\n    ### 👥 用户管理\n    - 用户CRUD操作\n    - 角色管理（管理员/普通用户/查看者）\n    - 用户信息维护\n\n    ### 📁 数据加载器\n    - 支持CSV/Excel文件上传\n    - 实体类型选择（product/hco）\n    - 实时处理进度跟踪\n    - 处理结果统计\n\n    ### 🔄 外部匹配\n    - 文件上传和数据审核\n    - 智能数据匹配\n    - 多系统数据分发\n    - 失败记录下载\n\n    ### 📊 进度监控\n    - 实时任务状态跟踪\n    - 详细进度信息\n    - 错误处理和报告\n\n    ---\n\n    **技术栈**: FastAPI + Python 3.10 + JWT + Pydantic\n\n    **认证方式**: Bearer Token (JWT)\n\n    **默认账号**:\n    - 管理员: admin/secret\n    - 普通用户: user/secret\n    ", "contact": {"name": "MDM开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT License"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "开发环境"}, {"url": "https://api.mdm.com", "description": "生产环境"}], "paths": {"/api/v1/auth/login": {"post": {"tags": ["认证"], "summary": "用户登录", "description": "用户登录接口\n\n- **username**: 用户名\n- **password**: 密码", "operationId": "login_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["认证"], "summary": "用户登出", "description": "用户登出接口\n\n注意：由于使用JWT，实际的登出需要在客户端删除token", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/me": {"get": {"tags": ["认证"], "summary": "获取当前用户信息", "description": "获取当前登录用户的信息", "operationId": "get_current_user_info_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/refresh": {"post": {"tags": ["认证"], "summary": "刷新令牌", "description": "刷新访问令牌", "operationId": "refresh_token_api_v1_auth_refresh_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/users/": {"get": {"tags": ["用户管理"], "summary": "获取用户列表", "description": "获取用户列表（仅管理员）\n\n- **skip**: 跳过的记录数\n- **limit**: 返回的记录数限制", "operationId": "get_users_api_v1_users__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}, "title": "Response Get Users Api V1 Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["用户管理"], "summary": "创建用户", "description": "创建新用户（仅管理员）", "operationId": "create_user_api_v1_users__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["用户管理"], "summary": "获取用户详情", "description": "获取指定用户的详情\n\n- 管理员可以查看所有用户\n- 普通用户只能查看自己的信息", "operationId": "get_user_api_v1_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["用户管理"], "summary": "更新用户", "description": "更新用户信息\n\n- 管理员可以更新所有用户\n- 普通用户只能更新自己的部分信息", "operationId": "update_user_api_v1_users__user_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["用户管理"], "summary": "删除用户", "description": "删除用户（仅管理员）", "operationId": "delete_user_api_v1_users__user_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/data-loader/upload": {"post": {"tags": ["数据加载器"], "summary": "上传文件并创建数据加载任务", "description": "上传文件并创建数据加载任务\n\n- **file**: 上传的文件\n- **entity_type**: 实体类型（product 或 hco）", "operationId": "upload_file_api_v1_data_loader_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_v1_data_loader_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataLoaderTask"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/data-loader/tasks": {"get": {"tags": ["数据加载器"], "summary": "获取数据加载任务列表", "description": "获取数据加载任务列表", "operationId": "get_tasks_api_v1_data_loader_tasks_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DataLoaderTask"}, "title": "Response Get Tasks Api V1 Data Loader Tasks Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/data-loader/tasks/{task_id}": {"get": {"tags": ["数据加载器"], "summary": "获取任务详情", "description": "获取指定任务的详情", "operationId": "get_task_api_v1_data_loader_tasks__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataLoaderTask"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/data-loader/tasks/{task_id}/progress": {"get": {"tags": ["数据加载器"], "summary": "获取任务进度", "description": "获取任务处理进度", "operationId": "get_task_progress_api_v1_data_loader_tasks__task_id__progress_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataLoaderProgress"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/data-loader/tasks/{task_id}/result": {"get": {"tags": ["数据加载器"], "summary": "获取任务结果", "description": "获取任务处理结果", "operationId": "get_task_result_api_v1_data_loader_tasks__task_id__result_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataLoaderResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/upload": {"post": {"tags": ["外部匹配"], "summary": "上传文件创建外部匹配任务", "description": "上传文件并创建外部匹配任务\n\n- **file**: 上传的文件", "operationId": "upload_file_api_v1_external_match_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_api_v1_external_match_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalMatchTask"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/external-match/tasks/{task_id}/review": {"get": {"tags": ["外部匹配"], "summary": "获取文件审核信息", "description": "获取文件审核信息", "operationId": "get_file_review_api_v1_external_match_tasks__task_id__review_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileReview"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/confirm": {"post": {"tags": ["外部匹配"], "summary": "确认文件并开始匹配", "description": "确认文件信息并开始匹配处理", "operationId": "confirm_and_start_matching_api_v1_external_match_tasks__task_id__confirm_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalMatchTask"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/progress": {"get": {"tags": ["外部匹配"], "summary": "获取匹配进度", "description": "获取匹配处理进度", "operationId": "get_match_progress_api_v1_external_match_tasks__task_id__progress_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchProgress"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/result": {"get": {"tags": ["外部匹配"], "summary": "获取匹配结果", "description": "获取匹配结果信息", "operationId": "get_match_result_api_v1_external_match_tasks__task_id__result_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchResult"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/distribute": {"post": {"tags": ["外部匹配"], "summary": "开始数据分发", "description": "开始数据分发到各关联系统", "operationId": "start_distribution_api_v1_external_match_tasks__task_id__distribute_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalMatchTask"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/distribution": {"get": {"tags": ["外部匹配"], "summary": "获取分发进度", "description": "获取数据分发进度", "operationId": "get_distribution_progress_api_v1_external_match_tasks__task_id__distribution_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DistributionProgress"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/external-match/tasks/{task_id}/download-errors": {"get": {"tags": ["外部匹配"], "summary": "下载失败记录", "description": "下载失败记录文件", "operationId": "download_error_records_api_v1_external_match_tasks__task_id__download_errors_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "获取系统信息", "description": "获取MDM系统基本信息", "operationId": "get_system_info__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemInfo"}}}}}}}, "/hello/{name}": {"get": {"summary": "个性化问候", "description": "个性化问候端点", "operationId": "say_hello_hello__name__get", "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string", "title": "Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "健康检查", "description": "系统健康检查端点", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}}, "components": {"schemas": {"Body_upload_file_api_v1_data_loader_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}, "entity_type": {"$ref": "#/components/schemas/EntityType"}}, "type": "object", "required": ["file", "entity_type"], "title": "Body_upload_file_api_v1_data_loader_upload_post"}, "Body_upload_file_api_v1_external_match_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_api_v1_external_match_upload_post"}, "DataLoaderProgress": {"properties": {"task_id": {"type": "integer", "title": "Task Id"}, "status": {"$ref": "#/components/schemas/ProcessStatus"}, "progress": {"type": "integer", "title": "Progress"}, "message": {"type": "string", "title": "Message"}, "total_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Records"}, "processed_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processed Records"}, "success_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Success Records"}, "failed_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Failed Records"}, "estimated_time_remaining": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimated Time Remaining"}}, "type": "object", "required": ["task_id", "status", "progress", "message"], "title": "DataLoaderProgress", "description": "数据加载器进度"}, "DataLoaderResult": {"properties": {"task_id": {"type": "integer", "title": "Task Id"}, "status": {"$ref": "#/components/schemas/ProcessStatus"}, "total_records": {"type": "integer", "title": "Total Records"}, "success_records": {"type": "integer", "title": "Success Records"}, "failed_records": {"type": "integer", "title": "Failed Records"}, "error_details": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "completed_at": {"type": "string", "format": "date-time", "title": "Completed At"}}, "type": "object", "required": ["task_id", "status", "total_records", "success_records", "failed_records", "completed_at"], "title": "DataLoaderResult", "description": "数据加载器结果"}, "DataLoaderTask": {"properties": {"id": {"type": "integer", "title": "Id"}, "file_name": {"type": "string", "title": "File Name"}, "file_path": {"type": "string", "title": "File Path"}, "entity_type": {"$ref": "#/components/schemas/EntityType"}, "status": {"$ref": "#/components/schemas/ProcessStatus"}, "progress": {"type": "integer", "title": "Progress", "default": 0}, "total_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Records"}, "processed_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processed Records"}, "success_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Success Records"}, "failed_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Failed Records"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"type": "integer", "title": "Created By"}}, "type": "object", "required": ["id", "file_name", "file_path", "entity_type", "status", "created_at", "created_by"], "title": "DataLoaderTask", "description": "数据加载器任务"}, "DistributionNode": {"properties": {"system": {"$ref": "#/components/schemas/SystemNode"}, "status": {"$ref": "#/components/schemas/ProcessStatus"}, "progress": {"type": "integer", "title": "Progress"}, "message": {"type": "string", "title": "Message"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["system", "status", "progress", "message"], "title": "DistributionNode", "description": "分发节点"}, "DistributionProgress": {"properties": {"task_id": {"type": "integer", "title": "Task Id"}, "batch_id": {"type": "string", "title": "Batch Id"}, "overall_status": {"$ref": "#/components/schemas/MatchStatus"}, "overall_progress": {"type": "integer", "title": "Overall Progress"}, "nodes": {"items": {"$ref": "#/components/schemas/DistributionNode"}, "type": "array", "title": "Nodes"}, "estimated_time_remaining": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimated Time Remaining"}}, "type": "object", "required": ["task_id", "batch_id", "overall_status", "overall_progress", "nodes"], "title": "DistributionProgress", "description": "分发进度"}, "EntityType": {"type": "string", "enum": ["product", "hco"], "title": "EntityType", "description": "实体类型枚举"}, "ExternalMatchTask": {"properties": {"id": {"type": "integer", "title": "Id"}, "batch_id": {"type": "string", "title": "Batch Id"}, "file_name": {"type": "string", "title": "File Name"}, "file_path": {"type": "string", "title": "File Path"}, "status": {"$ref": "#/components/schemas/MatchStatus"}, "progress": {"type": "integer", "title": "Progress", "default": 0}, "total_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Records"}, "matched_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Matched Records"}, "unmatched_records": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Unmatched Records"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "result_file_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result File Path"}, "result_data_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result Data Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"type": "integer", "title": "Created By"}}, "type": "object", "required": ["id", "batch_id", "file_name", "file_path", "status", "created_at", "created_by"], "title": "ExternalMatchTask", "description": "外部匹配任务"}, "FileReview": {"properties": {"file_name": {"type": "string", "title": "File Name"}, "file_path": {"type": "string", "title": "File Path"}, "file_size": {"type": "integer", "title": "File Size"}, "upload_time": {"type": "string", "format": "date-time", "title": "Upload Time"}, "data_summary": {"type": "object", "title": "Data Summary"}}, "type": "object", "required": ["file_name", "file_path", "file_size", "upload_time", "data_summary"], "title": "FileReview", "description": "文件审核信息"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "service": {"type": "string", "title": "Service"}, "version": {"type": "string", "title": "Version"}}, "type": "object", "required": ["status", "service", "version"], "title": "HealthResponse"}, "HelloResponse": {"properties": {"message": {"type": "string", "title": "Message"}, "status": {"type": "string", "title": "Status"}, "data": {"type": "object", "title": "Data", "default": {}}}, "type": "object", "required": ["message", "status"], "title": "HelloResponse"}, "LoginRequest": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "password"], "title": "LoginRequest", "description": "登录请求模型"}, "MatchProgress": {"properties": {"task_id": {"type": "integer", "title": "Task Id"}, "batch_id": {"type": "string", "title": "Batch Id"}, "status": {"$ref": "#/components/schemas/MatchStatus"}, "progress": {"type": "integer", "title": "Progress"}, "message": {"type": "string", "title": "Message"}, "current_step": {"type": "string", "title": "Current Step"}, "estimated_time_remaining": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimated Time Remaining"}}, "type": "object", "required": ["task_id", "batch_id", "status", "progress", "message", "current_step"], "title": "MatchProgress", "description": "匹配进度"}, "MatchResult": {"properties": {"task_id": {"type": "integer", "title": "Task Id"}, "batch_id": {"type": "string", "title": "Batch Id"}, "status": {"$ref": "#/components/schemas/MatchStatus"}, "result_file_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result File Path"}, "result_data_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Result Data Id"}, "total_records": {"type": "integer", "title": "Total Records"}, "matched_records": {"type": "integer", "title": "Matched Records"}, "unmatched_records": {"type": "integer", "title": "Unmatched Records"}, "match_rate": {"type": "number", "title": "Match Rate"}, "processing_time": {"type": "integer", "title": "Processing Time"}, "completed_at": {"type": "string", "format": "date-time", "title": "Completed At"}}, "type": "object", "required": ["task_id", "batch_id", "status", "total_records", "matched_records", "unmatched_records", "match_rate", "processing_time", "completed_at"], "title": "MatchResult", "description": "匹配结果"}, "MatchStatus": {"type": "string", "enum": ["uploaded", "reviewing", "processing", "matched", "distributing", "completed", "failed"], "title": "MatchStatus", "description": "匹配状态枚举"}, "ProcessStatus": {"type": "string", "enum": ["pending", "processing", "success", "failed"], "title": "ProcessStatus", "description": "处理状态枚举"}, "SystemInfo": {"properties": {"app_name": {"type": "string", "title": "App Name"}, "version": {"type": "string", "title": "Version"}, "description": {"type": "string", "title": "Description"}, "features": {"items": {}, "type": "array", "title": "Features"}}, "type": "object", "required": ["app_name", "version", "description", "features"], "title": "SystemInfo", "description": "系统信息模型"}, "SystemNode": {"type": "string", "enum": ["crm", "erp", "mdm", "data_warehouse"], "title": "SystemNode", "description": "系统节点枚举"}, "Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}}, "type": "object", "required": ["access_token", "token_type"], "title": "Token", "description": "令牌模型"}, "User": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "role": {"allOf": [{"$ref": "#/components/schemas/UserRole"}], "default": "user"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["username", "id", "created_at"], "title": "User", "description": "返回给前端的用户模型"}, "UserCreate": {"properties": {"username": {"type": "string", "title": "Username"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "role": {"allOf": [{"$ref": "#/components/schemas/UserRole"}], "default": "user"}, "is_active": {"type": "boolean", "title": "Is Active", "default": true}, "password": {"type": "string", "title": "Password"}}, "type": "object", "required": ["username", "password"], "title": "UserCreate", "description": "创建用户模型"}, "UserRole": {"type": "string", "enum": ["admin", "user", "viewer"], "title": "UserRole", "description": "用户角色枚举"}, "UserUpdate": {"properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "role": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}]}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}}, "type": "object", "title": "UserUpdate", "description": "更新用户模型"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}