<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据校验Mock测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #409eff;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #67c23a;
        }
        .expected-result h4 {
            color: #67c23a;
            margin: 0 0 10px 0;
        }
        .file-examples {
            background: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin-bottom: 15px;
        }
        .file-examples h4 {
            color: #856404;
            margin: 0 0 10px 0;
        }
        .file-examples ul {
            margin: 0;
            padding-left: 20px;
        }
        .access-link {
            display: inline-block;
            background: #409eff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin-top: 10px;
        }
        .access-link:hover {
            background: #337ecc;
        }
        .warning {
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            color: #f56c6c;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            color: #409eff;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>DataLoader 数据校验Mock测试指南</h1>
    
    <div class="success">
        ✅ 开发服务器已启动：<a href="http://localhost:3001" target="_blank">http://localhost:3001</a>
    </div>

    <div class="warning">
        ⚠️ 注意：当前使用Mock数据进行测试，后台接口开发完成后需要移除Mock实现。
    </div>

    <div class="file-examples">
        <h4>📁 测试文件命名规则</h4>
        <ul>
            <li><strong>校验成功</strong>：任意Excel文件，文件名不包含"error"或"fail"，大小&lt;10MB</li>
            <li><strong>校验失败</strong>：文件名包含"error"或"fail"的Excel文件，或大小&gt;10MB的文件</li>
        </ul>
        <p><strong>建议测试文件名</strong>：success_test.xlsx, error_test.xlsx, fail_data.xlsx</p>
    </div>

    <div class="test-card">
        <div class="test-title">🟢 测试场景1：数据校验成功</div>
        <div class="test-description">
            测试文件校验成功的完整流程，验证成功状态的UI显示和交互。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>访问 <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
                <li>登录系统（如需要）</li>
                <li>点击"数据加载器"进入功能页面</li>
                <li>上传一个Excel文件（文件名不包含"error"或"fail"）</li>
                <li>选择操作类型：创建 或 更新</li>
                <li>点击"开始校验"按钮</li>
                <li>等待2-4秒校验完成</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>显示转圈校验动画和"正在校验数据，请稍候..."提示</li>
                <li>校验完成后显示绿色成功图标</li>
                <li>显示"数据校验成功"标题和成功描述</li>
                <li>提供"上一步"和"确认并开始处理"两个按钮</li>
                <li>点击"确认并开始处理"进入数据处理步骤</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">🔴 测试场景2：数据校验失败</div>
        <div class="test-description">
            测试文件校验失败的完整流程，验证失败状态的UI显示和错误处理。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>准备一个文件名包含"error"的Excel文件（如：test_error.xlsx）</li>
                <li>按照场景1的步骤1-5操作</li>
                <li>点击"开始校验"按钮</li>
                <li>等待2-4秒校验完成</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>显示转圈校验动画和校验提示</li>
                <li>校验完成后显示红色错误图标</li>
                <li>显示"数据校验失败"标题和错误描述</li>
                <li>提供"下载失败报告"和"重新上传"两个按钮</li>
                <li>错误信息显示具体的失败原因</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">📥 测试场景3：下载错误报告</div>
        <div class="test-description">
            在校验失败的基础上，测试错误报告的下载功能。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>完成测试场景2，确保处于校验失败状态</li>
                <li>点击"下载失败报告"按钮</li>
                <li>等待1秒下载准备时间</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>自动下载CSV格式的错误报告文件</li>
                <li>文件名格式：validation_errors_{随机ID}.csv</li>
                <li>文件包含错误详情：行号、字段名、错误类型、错误描述、当前值、建议值</li>
                <li>可以用Excel或文本编辑器打开查看内容</li>
                <li>显示"错误报告下载成功"提示消息</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">🔄 测试场景4：重新上传</div>
        <div class="test-description">
            测试重新上传功能，验证状态重置和流程重新开始。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>完成测试场景2，确保处于校验失败状态</li>
                <li>点击"重新上传"按钮</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>返回到第一步"文件上传"页面</li>
                <li>清空之前选择的文件</li>
                <li>清空之前选择的操作类型</li>
                <li>清空校验结果状态</li>
                <li>步骤指示器显示当前在第1步</li>
                <li>可以重新开始整个流程</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">📱 测试场景5：响应式设计</div>
        <div class="test-description">
            测试在不同屏幕尺寸下的显示效果和交互体验。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>在桌面浏览器中完成基本功能测试</li>
                <li>使用浏览器开发者工具切换到移动设备视图</li>
                <li>重复执行上述测试场景</li>
                <li>测试不同屏幕尺寸：手机、平板、桌面</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>所有元素在不同屏幕尺寸下正常显示</li>
                <li>按钮和交互元素大小适中，易于点击</li>
                <li>文字清晰可读，不会出现截断</li>
                <li>步骤指示器在移动端正常显示</li>
                <li>上传区域在移动端易于操作</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <div class="test-title">🔧 调试技巧</div>
        <div class="test-description">
            如果测试过程中遇到问题，可以使用以下调试方法：
        </div>
        <div class="test-steps">
            <h4>调试方法：</h4>
            <ol>
                <li><strong>浏览器开发者工具</strong>：按F12打开，查看Console面板的错误信息</li>
                <li><strong>网络请求</strong>：在Network面板查看API调用情况</li>
                <li><strong>Vue DevTools</strong>：安装Vue开发者工具查看组件状态</li>
                <li><strong>修改Mock延迟</strong>：在src/api/dataLoader.js中调整延迟时间进行快速测试</li>
                <li><strong>强制失败测试</strong>：临时修改Mock逻辑强制返回失败结果</li>
            </ol>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <a href="http://localhost:3001" class="access-link" target="_blank">
            🚀 开始测试 DataLoader 功能
        </a>
    </div>

    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>📋 测试检查清单</h3>
        <ul style="list-style: none; padding: 0;">
            <li>☐ 校验成功流程完整</li>
            <li>☐ 校验失败流程完整</li>
            <li>☐ 错误报告下载正常</li>
            <li>☐ 重新上传功能正常</li>
            <li>☐ 步骤指示器显示正确</li>
            <li>☐ 加载动画显示正常</li>
            <li>☐ 响应式设计适配良好</li>
            <li>☐ 错误提示信息清晰</li>
            <li>☐ 按钮交互反馈及时</li>
            <li>☐ 整体用户体验流畅</li>
        </ul>
    </div>
</body>
</html>
