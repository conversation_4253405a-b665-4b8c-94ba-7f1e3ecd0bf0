# 前端集成指南

## 📋 API文档访问方式

### 1. 在线交互式文档（推荐）
- **Swagger UI**: http://localhost:8000/docs
  - 可直接测试API
  - 支持认证
  - 实时查看请求/响应
  
- **ReDoc**: http://localhost:8000/redoc
  - 更美观的文档展示
  - 适合阅读和参考

### 2. 机器可读格式
- **OpenAPI JSON**: http://localhost:8000/openapi.json
  - 标准OpenAPI 3.0格式
  - 可用于代码生成
  - 支持各种工具导入

## 🔧 前端开发建议

### 认证流程
```javascript
// 1. 登录获取token
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'secret'
  })
});

const { access_token } = await loginResponse.json();

// 2. 在后续请求中使用token
const apiResponse = await fetch('http://localhost:8000/api/v1/users/', {
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json',
  }
});
```

### 文件上传示例
```javascript
// 数据加载器文件上传
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('entity_type', 'product'); // 或 'hco'

const uploadResponse = await fetch('http://localhost:8000/api/v1/data-loader/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
  },
  body: formData
});
```

### 进度查询示例
```javascript
// 查询任务进度
const progressResponse = await fetch(
  `http://localhost:8000/api/v1/data-loader/tasks/${taskId}/progress`,
  {
    headers: {
      'Authorization': `Bearer ${access_token}`,
    }
  }
);

const progress = await progressResponse.json();
console.log(`进度: ${progress.percentage}%`);
```

## 📦 代码生成工具

### 使用OpenAPI Generator
```bash
# 安装工具
npm install @openapitools/openapi-generator-cli -g

# 生成TypeScript客户端
openapi-generator-cli generate \
  -i http://localhost:8000/openapi.json \
  -g typescript-fetch \
  -o ./src/api-client

# 生成JavaScript客户端
openapi-generator-cli generate \
  -i http://localhost:8000/openapi.json \
  -g javascript \
  -o ./src/api-client
```

### 使用Swagger Codegen
```bash
# 生成React客户端
swagger-codegen generate \
  -i http://localhost:8000/openapi.json \
  -l typescript-fetch \
  -o ./src/api
```

## 🎯 推荐的前端架构

### React + TypeScript 示例
```typescript
// api/client.ts
export class MDMApiClient {
  private baseURL = 'http://localhost:8000';
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers,
    };

    const response = await fetch(url, { ...options, headers });
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  }

  // 用户认证
  async login(username: string, password: string) {
    return this.request<{access_token: string}>('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  // 获取用户列表
  async getUsers() {
    return this.request<User[]>('/api/v1/users/');
  }

  // 上传文件
  async uploadFile(file: File, entityType: 'product' | 'hco') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('entity_type', entityType);

    return this.request<TaskResponse>('/api/v1/data-loader/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // 让浏览器自动设置Content-Type
    });
  }
}
```

### Vue.js 示例
```javascript
// composables/useApi.js
import { ref, reactive } from 'vue'

export function useApi() {
  const loading = ref(false)
  const error = ref(null)
  const token = ref(localStorage.getItem('token'))

  const api = reactive({
    baseURL: 'http://localhost:8000',
    
    async request(endpoint, options = {}) {
      loading.value = true
      error.value = null
      
      try {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
          headers: {
            'Content-Type': 'application/json',
            ...(token.value && { Authorization: `Bearer ${token.value}` }),
            ...options.headers,
          },
          ...options,
        })
        
        if (!response.ok) {
          throw new Error(`API Error: ${response.status}`)
        }
        
        return await response.json()
      } catch (err) {
        error.value = err.message
        throw err
      } finally {
        loading.value = false
      }
    },

    async login(username, password) {
      const result = await this.request('/api/v1/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password }),
      })
      
      token.value = result.access_token
      localStorage.setItem('token', result.access_token)
      return result
    }
  })

  return { api, loading, error, token }
}
```

## 🔄 实时更新

### WebSocket支持（未来扩展）
```javascript
// 连接WebSocket获取实时进度更新
const ws = new WebSocket('ws://localhost:8000/ws/progress');

ws.onmessage = (event) => {
  const progress = JSON.parse(event.data);
  updateProgressBar(progress.percentage);
};
```

## 📱 移动端支持

所有API都支持CORS，可以直接在移动端应用中使用：
- React Native
- Flutter
- 原生iOS/Android应用

## 🛠️ 开发工具

1. **Postman**: 导入生成的collection文件进行API测试
2. **Insomnia**: 支持OpenAPI规范导入
3. **VS Code REST Client**: 使用.http文件测试API
4. **Chrome DevTools**: 调试网络请求

## 📞 技术支持

如需要自定义API文档格式或有其他集成问题，请联系开发团队。
