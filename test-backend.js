// 简单的后端API测试脚本
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

async function testBackendAPI() {
  console.log('开始测试后端API连接...\n');

  // 测试健康检查
  try {
    console.log('1. 测试健康检查 API...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ 健康检查成功:', healthResponse.data);
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('   后端服务器可能未启动，请确保后端服务运行在 http://localhost:8000');
      return;
    }
  }

  // 测试系统信息
  try {
    console.log('\n2. 测试系统信息 API...');
    const systemResponse = await axios.get(`${API_BASE_URL}/system/info`);
    console.log('✅ 系统信息获取成功:', systemResponse.data);
  } catch (error) {
    console.log('❌ 系统信息获取失败:', error.response?.data || error.message);
  }

  // 测试API文档
  try {
    console.log('\n3. 测试API文档访问...');
    const docsResponse = await axios.get(`${API_BASE_URL}/docs`);
    console.log('✅ API文档可访问');
  } catch (error) {
    console.log('❌ API文档访问失败:', error.message);
  }

  // 测试OpenAPI规范
  try {
    console.log('\n4. 测试OpenAPI规范...');
    const openApiResponse = await axios.get(`${API_BASE_URL}/openapi.json`);
    console.log('✅ OpenAPI规范可访问，包含', Object.keys(openApiResponse.data.paths).length, '个端点');
  } catch (error) {
    console.log('❌ OpenAPI规范访问失败:', error.message);
  }

  console.log('\n测试完成！');
}

// 运行测试
testBackendAPI().catch(console.error);
