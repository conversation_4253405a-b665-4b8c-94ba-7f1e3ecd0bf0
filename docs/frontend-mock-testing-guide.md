# 前端Mock测试指南

## 概述

本文档说明如何在后台接口开发完成前，使用Mock数据测试DataLoader数据校验功能。

## Mock实现说明

### 当前Mock逻辑

在 `src/api/dataLoader.js` 中，`validateFile` 方法已实现Mock逻辑：

1. **校验成功条件**：
   - 文件名不包含 "error" 或 "fail"
   - 文件大小小于等于10MB

2. **校验失败条件**：
   - 文件名包含 "error" 或 "fail"
   - 文件大小超过10MB

3. **校验时间**：2-4秒随机延迟，模拟真实校验时间

### Mock响应数据格式

**成功响应示例**：
```javascript
{
  success: true,
  validation_id: 8765,
  message: '数据校验成功，所有数据格式正确',
  record_count: 234,
  details: {
    total_records: 234,
    valid_records: 234,
    entity_type: 'product'
  }
}
```

**失败响应示例**：
```javascript
{
  success: false,
  validation_id: 4321,
  error: '数据校验失败：发现格式错误或必填字段缺失',
  error_count: 15,
  details: {
    format_errors: 8,
    missing_fields: 4,
    invalid_values: 3
  }
}
```

## 测试场景

### 1. 校验成功场景

**测试步骤**：
1. 访问 http://localhost:3001
2. 登录系统
3. 进入"数据加载器"页面
4. 上传任意Excel文件（文件名不包含"error"或"fail"，大小<10MB）
5. 选择操作类型（创建或更新）
6. 点击"开始校验"

**预期结果**：
- 显示转圈校验动画（2-4秒）
- 显示"数据校验成功"页面
- 提供"上一步"和"确认并开始处理"按钮

### 2. 校验失败场景

**测试步骤**：
1. 准备一个文件名包含"error"的Excel文件（如：test_error.xlsx）
2. 按照成功场景的步骤1-5操作
3. 点击"开始校验"

**预期结果**：
- 显示转圈校验动画（2-4秒）
- 显示"数据校验失败"页面
- 显示错误信息
- 提供"下载失败报告"和"重新上传"按钮

### 3. 下载错误报告场景

**测试步骤**：
1. 在校验失败场景基础上
2. 点击"下载失败报告"按钮

**预期结果**：
- 自动下载CSV格式的错误报告文件
- 文件名格式：validation_errors_{validation_id}.csv
- 文件包含错误详情（行号、字段名、错误类型等）

### 4. 重新上传场景

**测试步骤**：
1. 在校验失败场景基础上
2. 点击"重新上传"按钮

**预期结果**：
- 返回到第一步"文件上传"页面
- 清空之前的文件选择和操作类型
- 重置校验结果

## 测试文件准备

### 建议的测试文件

1. **success_test.xlsx** - 校验成功测试
2. **error_test.xlsx** - 校验失败测试  
3. **fail_data.xlsx** - 校验失败测试
4. **large_file.xlsx** - 大文件测试（>10MB）
5. **normal_data.xlsx** - 正常文件测试

### Excel文件内容示例

可以创建包含以下列的Excel文件：
- 产品名称
- 产品编码  
- 分类
- 价格
- 生产日期
- 描述

## UI测试要点

### 1. 步骤指示器
- 确认显示5个步骤
- 当前步骤高亮显示
- 步骤标题正确

### 2. 校验中状态
- 转圈动画正常显示
- 提示文字"正在校验数据，请稍候..."
- 无其他可操作按钮

### 3. 校验成功状态
- 成功图标显示
- 成功提示信息
- "上一步"和"确认并开始处理"按钮可用

### 4. 校验失败状态
- 错误图标显示
- 错误信息显示
- "下载失败报告"按钮（有validation_id时显示）
- "重新上传"按钮

### 5. 响应式设计
- 在不同屏幕尺寸下布局正常
- 移动端适配良好

## 调试技巧

### 1. 浏览器开发者工具
- 查看Network面板，确认API调用
- 查看Console面板，检查错误信息
- 使用Vue DevTools查看组件状态

### 2. 修改Mock延迟时间
在 `src/api/dataLoader.js` 中修改延迟时间：
```javascript
// 修改这行来调整校验时间
}, 2000 + Math.random() * 2000) // 改为更短的时间用于快速测试
```

### 3. 强制校验失败
临时修改Mock逻辑，强制返回失败结果：
```javascript
// 在validateFile方法中添加
if (true) { // 强制失败
  resolve({
    success: false,
    // ... 失败响应数据
  })
  return
}
```

## 切换到真实API

当后台接口开发完成后，按以下步骤切换：

1. **移除Mock实现**：
   删除 `src/api/dataLoader.js` 中 `validateFile` 和 `downloadValidationErrors` 方法的Mock实现

2. **恢复真实API调用**：
```javascript
async validateFile(file, entityType) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('entity_type', entityType)
  
  const response = await http.post(API_ENDPOINTS.DATA_LOADER.VALIDATE, formData)
  return response
},

async downloadValidationErrors(validationId) {
  const response = await http.get(API_ENDPOINTS.DATA_LOADER.DOWNLOAD_VALIDATION_ERRORS(validationId), {
    responseType: 'blob'
  })
  return response
}
```

3. **测试真实接口**：
   使用相同的测试场景验证真实接口功能

## 常见问题

### Q: 校验一直显示加载状态
A: 检查浏览器Console是否有JavaScript错误，确认Mock方法正确执行

### Q: 下载的错误报告打不开
A: 确认浏览器支持CSV文件下载，可以用文本编辑器打开查看内容

### Q: 重新上传后状态没有重置
A: 检查 `resetToUpload` 方法是否正确清空了所有状态

### Q: 步骤指示器显示不正确
A: 确认 `currentStep` 的值在各个操作中正确更新

## 性能测试

### 大文件测试
- 准备10MB以上的Excel文件
- 测试校验失败场景
- 确认错误提示正确显示

### 并发测试
- 同时打开多个浏览器标签
- 并行执行校验操作
- 确认各个实例独立工作

通过以上测试，可以确保数据校验功能在后台接口开发完成前就能完整验证前端逻辑的正确性。
