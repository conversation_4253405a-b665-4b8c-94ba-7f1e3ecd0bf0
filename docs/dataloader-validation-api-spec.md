# DataLoader 数据校验接口开发说明

## 概述

本文档描述了MDM系统DataLoader模块中数据校验功能的后台接口规范。数据校验是在用户选择操作类型后、数据处理前的一个独立步骤，用于验证上传文件的数据格式和内容是否符合要求。

## 业务流程

```
文件上传 → 选择类型 → 数据校验 → 数据处理 → 完成
                        ↑
                   新增的校验步骤
```

## 接口规范

### 1. 数据校验接口

#### 接口信息
- **URL**: `POST /api/v1/data-loader/validate`
- **Content-Type**: `multipart/form-data`
- **功能**: 验证上传文件的数据格式和内容

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 上传的数据文件（Excel格式） |
| entity_type | string | 是 | 实体类型，枚举值：`product`（创建）、`hco`（更新） |

#### 响应格式

**校验成功响应**:
```json
{
  "success": true,
  "validation_id": 12345,
  "message": "数据校验成功，所有数据格式正确",
  "record_count": 150,
  "details": {
    "total_records": 150,
    "valid_records": 150,
    "entity_type": "product",
    "validation_time": "2024-01-15T10:30:00Z"
  }
}
```

**校验失败响应**:
```json
{
  "success": false,
  "validation_id": 12346,
  "error": "数据校验失败：发现格式错误或必填字段缺失",
  "error_count": 25,
  "details": {
    "total_records": 150,
    "valid_records": 125,
    "format_errors": 10,
    "missing_fields": 8,
    "invalid_values": 7,
    "entity_type": "product",
    "validation_time": "2024-01-15T10:30:00Z"
  }
}
```

#### 校验规则

**Product（创建）实体校验规则**:
- 必填字段：产品名称、产品编码、分类、价格
- 格式校验：
  - 产品编码：字母数字组合，不含特殊字符
  - 价格：数字格式，大于等于0
  - 生产日期：YYYY-MM-DD格式
  - 分类：枚举值（药品/器械/耗材）

**HCO（更新）实体校验规则**:
- 必填字段：机构名称、机构编码、机构类型
- 格式校验：
  - 机构编码：唯一性校验
  - 联系电话：手机号或固话格式
  - 邮箱：邮箱格式校验
  - 机构类型：枚举值（医院/诊所/药店）

### 2. 下载校验错误报告接口

#### 接口信息
- **URL**: `GET /api/v1/data-loader/validation/{validation_id}/download-errors`
- **功能**: 下载校验失败的详细错误报告

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| validation_id | int | 是 | 校验ID（从校验接口响应中获取） |

#### 响应格式
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Content-Disposition**: `attachment; filename="validation_errors_{validation_id}.xlsx"`

#### 错误报告Excel格式
| 列名 | 说明 | 示例 |
|------|------|------|
| 行号 | 数据在原文件中的行号 | 2 |
| 字段名 | 出错的字段名称 | 产品名称 |
| 错误类型 | 错误分类 | 必填字段缺失 |
| 错误描述 | 详细错误说明 | 产品名称不能为空 |
| 当前值 | 当前的错误值 | (空) |
| 建议值 | 修正建议 | 请填写产品名称 |

## 实现建议

### 1. 数据库设计

```sql
-- 校验记录表
CREATE TABLE validation_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    validation_id VARCHAR(50) UNIQUE NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    total_records INT NOT NULL,
    valid_records INT NOT NULL,
    error_count INT NOT NULL,
    success BOOLEAN NOT NULL,
    validation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_report_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 校验错误详情表
CREATE TABLE validation_errors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    validation_id VARCHAR(50) NOT NULL,
    row_number INT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    error_type VARCHAR(100) NOT NULL,
    error_description TEXT NOT NULL,
    current_value TEXT,
    suggested_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (validation_id) REFERENCES validation_records(validation_id)
);
```

### 2. 处理流程

1. **接收文件**: 解析上传的Excel文件
2. **数据读取**: 读取所有数据行
3. **逐行校验**: 根据entity_type应用相应校验规则
4. **记录错误**: 收集所有校验错误信息
5. **生成报告**: 如有错误，生成Excel错误报告
6. **返回结果**: 返回校验结果和统计信息

### 3. 错误处理

- **文件格式错误**: 返回HTTP 400，提示文件格式不支持
- **文件过大**: 返回HTTP 413，提示文件大小超限
- **系统错误**: 返回HTTP 500，记录详细错误日志

### 4. 性能优化

- 使用流式读取处理大文件
- 批量插入错误记录到数据库
- 异步生成错误报告文件
- 设置合理的文件大小限制（建议10MB）

## 前端集成说明

前端已实现完整的校验流程UI，包括：
- 校验中的加载动画
- 校验成功的确认界面
- 校验失败的错误提示和报告下载
- 重新上传功能

当前使用Mock数据进行测试，后台接口开发完成后，只需移除`src/api/dataLoader.js`中的Mock实现即可。

## 测试建议

### 1. 单元测试
- 各种校验规则的正确性
- 错误信息的准确性
- 边界条件处理

### 2. 集成测试
- 完整的校验流程
- 错误报告生成和下载
- 大文件处理性能

### 3. 测试数据
建议准备以下测试文件：
- 完全正确的数据文件
- 包含各种错误类型的数据文件
- 大容量数据文件（性能测试）
- 格式错误的文件（非Excel格式）

## 代码示例

### 1. Python FastAPI 实现示例

```python
from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse
import pandas as pd
import uuid
from datetime import datetime
from typing import Dict, List, Any

router = APIRouter(prefix="/api/v1/data-loader", tags=["data-loader"])

@router.post("/validate")
async def validate_file(
    file: UploadFile = File(...),
    entity_type: str = Form(...)
):
    """数据校验接口"""

    # 验证文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="不支持的文件格式")

    # 验证实体类型
    if entity_type not in ['product', 'hco']:
        raise HTTPException(status_code=400, detail="无效的实体类型")

    try:
        # 读取Excel文件
        df = pd.read_excel(file.file)
        validation_id = str(uuid.uuid4())

        # 执行校验
        validation_result = await validate_data(df, entity_type, validation_id)

        return validation_result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

async def validate_data(df: pd.DataFrame, entity_type: str, validation_id: str) -> Dict[str, Any]:
    """执行数据校验逻辑"""

    errors = []
    total_records = len(df)

    if entity_type == 'product':
        errors = validate_product_data(df)
    elif entity_type == 'hco':
        errors = validate_hco_data(df)

    error_count = len(errors)
    valid_records = total_records - error_count
    success = error_count == 0

    # 保存校验记录到数据库
    await save_validation_record(validation_id, entity_type, total_records,
                                valid_records, error_count, success, errors)

    if success:
        return {
            "success": True,
            "validation_id": validation_id,
            "message": "数据校验成功，所有数据格式正确",
            "record_count": total_records,
            "details": {
                "total_records": total_records,
                "valid_records": valid_records,
                "entity_type": entity_type,
                "validation_time": datetime.now().isoformat()
            }
        }
    else:
        return {
            "success": False,
            "validation_id": validation_id,
            "error": "数据校验失败：发现格式错误或必填字段缺失",
            "error_count": error_count,
            "details": {
                "total_records": total_records,
                "valid_records": valid_records,
                "format_errors": len([e for e in errors if e['error_type'] == '格式错误']),
                "missing_fields": len([e for e in errors if e['error_type'] == '必填字段缺失']),
                "invalid_values": len([e for e in errors if e['error_type'] == '数据类型错误']),
                "entity_type": entity_type,
                "validation_time": datetime.now().isoformat()
            }
        }

def validate_product_data(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """产品数据校验"""
    errors = []
    required_fields = ['产品名称', '产品编码', '分类', '价格']

    for index, row in df.iterrows():
        row_number = index + 2  # Excel行号从2开始（第1行是标题）

        # 必填字段校验
        for field in required_fields:
            if pd.isna(row.get(field)) or str(row.get(field)).strip() == '':
                errors.append({
                    'row_number': row_number,
                    'field_name': field,
                    'error_type': '必填字段缺失',
                    'error_description': f'{field}不能为空',
                    'current_value': '',
                    'suggested_value': f'请填写{field}'
                })

        # 格式校验
        if '产品编码' in row and not pd.isna(row['产品编码']):
            product_code = str(row['产品编码'])
            if not product_code.replace('-', '').replace('_', '').isalnum():
                errors.append({
                    'row_number': row_number,
                    'field_name': '产品编码',
                    'error_type': '格式错误',
                    'error_description': '产品编码只能包含字母、数字、连字符和下划线',
                    'current_value': product_code,
                    'suggested_value': '例如：PROD001 或 PROD-001'
                })

        # 价格校验
        if '价格' in row and not pd.isna(row['价格']):
            try:
                price = float(row['价格'])
                if price < 0:
                    errors.append({
                        'row_number': row_number,
                        'field_name': '价格',
                        'error_type': '数据类型错误',
                        'error_description': '价格不能为负数',
                        'current_value': str(row['价格']),
                        'suggested_value': '请输入大于等于0的数字'
                    })
            except (ValueError, TypeError):
                errors.append({
                    'row_number': row_number,
                    'field_name': '价格',
                    'error_type': '数据类型错误',
                    'error_description': '价格必须为数字',
                    'current_value': str(row['价格']),
                    'suggested_value': '请输入数字格式的价格'
                })

    return errors

@router.get("/validation/{validation_id}/download-errors")
async def download_validation_errors(validation_id: str):
    """下载校验错误报告"""

    # 从数据库获取错误记录
    errors = await get_validation_errors(validation_id)

    if not errors:
        raise HTTPException(status_code=404, detail="未找到校验错误记录")

    # 生成Excel错误报告
    error_file_path = await generate_error_report(validation_id, errors)

    return FileResponse(
        path=error_file_path,
        filename=f"validation_errors_{validation_id}.xlsx",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
```

### 2. 数据库操作示例

```python
from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

Base = declarative_base()

class ValidationRecord(Base):
    __tablename__ = "validation_records"

    id = Column(Integer, primary_key=True, autoincrement=True)
    validation_id = Column(String(50), unique=True, nullable=False)
    file_name = Column(String(255), nullable=False)
    entity_type = Column(String(50), nullable=False)
    total_records = Column(Integer, nullable=False)
    valid_records = Column(Integer, nullable=False)
    error_count = Column(Integer, nullable=False)
    success = Column(Boolean, nullable=False)
    validation_time = Column(DateTime, default=datetime.now)
    error_report_path = Column(String(500))
    created_at = Column(DateTime, default=datetime.now)

class ValidationError(Base):
    __tablename__ = "validation_errors"

    id = Column(Integer, primary_key=True, autoincrement=True)
    validation_id = Column(String(50), nullable=False)
    row_number = Column(Integer, nullable=False)
    field_name = Column(String(100), nullable=False)
    error_type = Column(String(100), nullable=False)
    error_description = Column(Text, nullable=False)
    current_value = Column(Text)
    suggested_value = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

async def save_validation_record(validation_id: str, entity_type: str,
                               total_records: int, valid_records: int,
                               error_count: int, success: bool, errors: List[Dict]):
    """保存校验记录"""
    # 实现数据库保存逻辑
    pass

async def get_validation_errors(validation_id: str) -> List[Dict]:
    """获取校验错误记录"""
    # 实现数据库查询逻辑
    pass
```

## 部署和监控

### 1. 配置建议
```yaml
# application.yml
data-loader:
  validation:
    max-file-size: 10MB
    allowed-extensions: ['.xlsx', '.xls']
    temp-dir: '/tmp/validation'
    report-retention-days: 30
```

### 2. 监控指标
- 校验请求数量和成功率
- 平均校验时间
- 错误报告下载次数
- 文件大小分布
- 错误类型统计

### 3. 日志记录
```python
import logging

logger = logging.getLogger(__name__)

# 记录校验开始
logger.info(f"开始校验文件: {file.filename}, 实体类型: {entity_type}")

# 记录校验结果
logger.info(f"校验完成: validation_id={validation_id}, 成功={success}, 错误数={error_count}")

# 记录错误
logger.error(f"校验失败: {str(e)}", exc_info=True)
```
