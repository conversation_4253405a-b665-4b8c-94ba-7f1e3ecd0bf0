# MDM Frontend Mock实现指南

## 概述

本文档说明了MDM前端系统中login、dataloader和用户管理功能的Mock实现，使这些功能可以在不依赖后台API的情况下正常运行。

## 🎯 已实现的Mock功能

### 1. 登录功能 (Login)
- ✅ Mock用户认证
- ✅ Token生成和验证
- ✅ 用户信息获取
- ✅ 登出功能
- ✅ Token刷新

### 2. 用户管理 (User Management)
- ✅ 用户列表查询
- ✅ 用户创建
- ✅ 用户编辑
- ✅ 用户删除
- ✅ 用户搜索和筛选

### 3. 数据加载器 (DataLoader)
- ✅ 文件上传和验证
- ✅ 数据校验
- ✅ 任务创建和状态跟踪
- ✅ 进度监控
- ✅ 错误报告下载

## 📁 修改的文件

| 文件路径 | 修改内容 |
|---------|---------|
| `src/api/auth.js` | 完全替换为Mock实现 |
| `src/api/users.js` | 完全替换为Mock实现 |
| `src/api/dataLoader.js` | 扩展现有Mock实现 |
| `src/api/mockData.js` | 新增：共享Mock数据存储 |

## 🔧 Mock实现详情

### 登录功能Mock

#### 预设测试账户
```javascript
用户名: admin     密码: admin     角色: 管理员
用户名: user1     密码: password  角色: 普通用户
用户名: viewer1   密码: password  角色: 查看者
用户名: test      密码: test      角色: 普通用户
```

#### 功能特性
- 模拟1秒网络延迟
- 真实的用户名/密码验证
- 生成带时间戳的Mock Token
- 自动用户信息获取
- 支持Token刷新

### 用户管理功能Mock

#### 预设用户数据
系统预置5个测试用户，包含不同角色和状态。

#### 功能特性
- 完整的CRUD操作
- 用户名和邮箱唯一性验证
- 防止删除admin用户
- 支持分页查询
- 模拟真实的网络延迟

#### 验证规则
- 用户名不能为空且唯一
- 密码不能为空
- 邮箱不能为空且唯一
- 角色必须是有效值

### 数据加载器功能Mock

#### 文件验证
- 支持格式：CSV, Excel (.xlsx, .xls)
- 文件大小限制：10MB
- 实体类型：product, hco

#### 任务状态模拟
```
pending (0%) → processing (25%) → processing (75%) → success/failed (100%)
```

#### 成功/失败逻辑
- 文件名包含"error"或"fail"：模拟失败
- 其他情况：模拟成功
- 自动生成随机的处理统计数据

#### 校验功能
- 2-4秒随机延迟模拟校验时间
- 根据文件名决定校验结果
- 生成详细的错误报告
- 支持错误报告下载

## 🚀 使用方法

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 测试登录功能
1. 访问 http://localhost:3000/login
2. 使用预设账户登录：
   - 用户名：admin
   - 密码：admin

### 3. 测试用户管理
1. 登录后访问用户管理页面
2. 尝试创建、编辑、删除用户
3. 测试搜索和筛选功能

### 4. 测试数据加载器
1. 访问数据加载器页面
2. 上传测试文件：
   - 成功测试：上传正常命名的Excel文件
   - 失败测试：上传包含"error"的文件
3. 观察任务状态变化和进度更新

## 🧪 测试场景

### 登录测试
- ✅ 正确用户名密码登录
- ✅ 错误用户名密码登录
- ✅ 登出功能
- ✅ Token自动刷新

### 用户管理测试
- ✅ 查看用户列表
- ✅ 创建新用户（成功/失败）
- ✅ 编辑用户信息
- ✅ 删除用户（普通用户/admin用户）
- ✅ 搜索用户
- ✅ 角色筛选

### 数据加载器测试
- ✅ 文件上传（成功/失败）
- ✅ 数据校验（成功/失败）
- ✅ 任务状态跟踪
- ✅ 进度监控
- ✅ 错误报告下载

## 🔍 调试和故障排除

### 常见问题

1. **登录失败**
   - 检查用户名密码是否正确
   - 查看浏览器控制台错误信息

2. **用户操作失败**
   - 检查是否有重复的用户名或邮箱
   - 确认必填字段已填写

3. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过10MB

4. **任务状态不更新**
   - 等待足够时间（6秒完成状态更新）
   - 检查浏览器控制台是否有错误

### 调试工具

1. **浏览器开发者工具**
   - Console：查看错误日志
   - Network：监控API调用（虽然是Mock）
   - Application：查看localStorage中的token

2. **Vue DevTools**
   - 监控Store状态变化
   - 查看组件数据

3. **Mock数据状态**
   ```javascript
   // 在浏览器控制台执行
   import { getMockDataStatus } from '@/api/mockData'
   console.log(getMockDataStatus())
   ```

## 📊 Mock数据管理

### 重置Mock数据
```javascript
import { resetMockData } from '@/api/mockData'
resetMockData() // 清空所有Mock数据
```

### 查看Mock数据状态
```javascript
import { getMockDataStatus } from '@/api/mockData'
console.log(getMockDataStatus())
```

## 🔄 切换到真实API

当后台API开发完成后，只需要：

1. **移除Mock实现**
   - 恢复 `src/api/auth.js` 中的HTTP调用
   - 恢复 `src/api/users.js` 中的HTTP调用
   - 恢复 `src/api/dataLoader.js` 中的HTTP调用

2. **删除Mock文件**
   - 删除 `src/api/mockData.js`

3. **测试真实API**
   - 使用相同的测试场景验证真实接口

## 📈 性能考虑

### Mock实现优化
- 使用合理的网络延迟模拟
- 避免过大的Mock数据集
- 实现分页和搜索功能
- 使用内存存储，重启后重置

### 真实API建议
- 实现相同的接口规范
- 保持相同的数据结构
- 添加适当的错误处理
- 考虑性能优化

## 📞 技术支持

如有问题或需要协助：

1. 查看浏览器控制台错误信息
2. 检查Mock数据状态
3. 参考本文档的测试场景
4. 使用调试工具进行排查

---

**状态**: ✅ Mock实现完成，可独立运行
**版本**: v1.0.0
**更新时间**: 2024-01-15
