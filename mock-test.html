<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDM Frontend Mock功能测试指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .test-section {
            background-color: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .credentials {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
        }
        .credentials h4 {
            margin-top: 0;
            color: #856404;
        }
        .credential-item {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #27ae60;
            font-weight: bold;
        }
        .test-steps {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .url-link {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 10px 0;
        }
        .url-link a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .url-link a:hover {
            text-decoration: underline;
        }
        .file-examples {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .file-examples h4 {
            margin-top: 0;
            color: #495057;
        }
        .file-example {
            font-family: 'Courier New', monospace;
            background-color: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
        .success-example {
            border-left: 4px solid #28a745;
        }
        .fail-example {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MDM Frontend Mock功能测试指南</h1>
        
        <div class="info">
            <strong>📋 测试说明：</strong> 本系统已实现完整的Mock功能，login、dataloader和用户管理功能均可在不依赖后台API的情况下正常运行。
        </div>

        <div class="url-link">
            <a href="http://localhost:3000" target="_blank">🚀 打开MDM系统 - http://localhost:3000</a>
        </div>

        <h2>🔐 1. 登录功能测试 <span class="status success">Mock已实现</span></h2>
        
        <div class="test-section">
            <h3>预设测试账户</h3>
            <div class="credentials">
                <h4>可用的登录账户：</h4>
                <div class="credential-item">用户名: <strong>admin</strong> | 密码: <strong>admin</strong> | 角色: 管理员</div>
                <div class="credential-item">用户名: <strong>user1</strong> | 密码: <strong>password</strong> | 角色: 普通用户</div>
                <div class="credential-item">用户名: <strong>viewer1</strong> | 密码: <strong>password</strong> | 角色: 查看者</div>
                <div class="credential-item">用户名: <strong>test</strong> | 密码: <strong>test</strong> | 角色: 普通用户</div>
            </div>

            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>访问登录页面：<a href="http://localhost:3000/login" target="_blank">http://localhost:3000/login</a></li>
                    <li>使用上述任一账户登录</li>
                    <li>验证登录成功后跳转到数据加载器页面</li>
                    <li>测试登出功能</li>
                    <li>尝试错误的用户名密码，验证错误提示</li>
                </ol>
            </div>

            <ul class="feature-list">
                <li>模拟1秒网络延迟</li>
                <li>真实的用户名/密码验证</li>
                <li>自动生成Mock Token</li>
                <li>用户信息自动获取</li>
                <li>支持Token刷新</li>
            </ul>
        </div>

        <h2>👥 2. 用户管理功能测试 <span class="status success">Mock已实现</span></h2>
        
        <div class="test-section">
            <h3>功能特性</h3>
            <ul class="feature-list">
                <li>查看用户列表（预置5个测试用户）</li>
                <li>创建新用户（带验证）</li>
                <li>编辑用户信息</li>
                <li>删除用户（保护admin用户）</li>
                <li>用户搜索和角色筛选</li>
                <li>分页功能</li>
            </ul>

            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>登录后访问用户管理页面</li>
                    <li>查看预置的用户列表</li>
                    <li>点击"新增用户"创建用户</li>
                    <li>尝试创建重复用户名，验证错误提示</li>
                    <li>编辑现有用户信息</li>
                    <li>尝试删除admin用户，验证保护机制</li>
                    <li>删除普通用户</li>
                    <li>测试搜索和筛选功能</li>
                </ol>
            </div>

            <div class="warning">
                <strong>⚠️ 注意：</strong> admin用户受保护，无法删除。用户名和邮箱必须唯一。
            </div>
        </div>

        <h2>📊 3. 数据加载器功能测试 <span class="status success">Mock已实现</span></h2>
        
        <div class="test-section">
            <h3>支持的文件格式</h3>
            <ul class="feature-list">
                <li>CSV文件 (.csv)</li>
                <li>Excel文件 (.xlsx, .xls)</li>
                <li>文件大小限制：10MB</li>
                <li>实体类型：产品(product)、医疗机构(hco)</li>
            </ul>

            <h3>测试文件示例</h3>
            <div class="file-examples">
                <h4>成功测试文件（这些文件名会模拟成功）：</h4>
                <div class="file-example success-example">✅ products.xlsx</div>
                <div class="file-example success-example">✅ hco_data.csv</div>
                <div class="file-example success-example">✅ test_data.xlsx</div>
                
                <h4>失败测试文件（这些文件名会模拟失败）：</h4>
                <div class="file-example fail-example">❌ products_error.xlsx</div>
                <div class="file-example fail-example">❌ data_fail.csv</div>
                <div class="file-example fail-example">❌ error_test.xlsx</div>
            </div>

            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>访问数据加载器页面</li>
                    <li>上传一个正常命名的Excel文件（如：products.xlsx）</li>
                    <li>选择实体类型（产品或HCO）</li>
                    <li>进行数据校验，观察校验成功</li>
                    <li>确认并开始处理，观察进度更新</li>
                    <li>上传包含"error"的文件名，测试失败场景</li>
                    <li>在校验失败时下载错误报告</li>
                </ol>
            </div>

            <h3>任务状态流程</h3>
            <div class="info">
                <strong>状态变化：</strong> 等待中(0%) → 处理中(25%) → 处理中(75%) → 成功/失败(100%)
                <br><strong>时间：</strong> 整个过程约6秒完成
            </div>

            <ul class="feature-list">
                <li>文件上传验证</li>
                <li>数据校验（2-4秒延迟）</li>
                <li>任务状态实时跟踪</li>
                <li>进度条动画</li>
                <li>错误报告生成和下载</li>
                <li>成功/失败统计数据</li>
            </ul>
        </div>

        <h2>🔧 4. 调试和故障排除</h2>
        
        <div class="test-section">
            <h3>常见问题</h3>
            <ul>
                <li><strong>登录失败：</strong> 检查用户名密码是否正确，查看控制台错误</li>
                <li><strong>用户操作失败：</strong> 检查是否有重复的用户名或邮箱</li>
                <li><strong>文件上传失败：</strong> 检查文件格式和大小限制</li>
                <li><strong>任务状态不更新：</strong> 等待6秒让状态更新完成</li>
            </ul>

            <h3>调试工具</h3>
            <ul>
                <li><strong>浏览器开发者工具：</strong> F12打开，查看Console和Network面板</li>
                <li><strong>Vue DevTools：</strong> 监控Store状态变化</li>
                <li><strong>localStorage：</strong> 查看存储的token和用户信息</li>
            </ul>
        </div>

        <h2>📈 5. Mock数据状态</h2>
        
        <div class="test-section">
            <div class="info">
                <strong>💡 提示：</strong> 所有Mock数据存储在内存中，刷新页面或重启服务器会重置数据。
            </div>
            
            <h3>数据重置</h3>
            <p>如需重置所有Mock数据，可以刷新页面或重启开发服务器。</p>
            
            <h3>数据持久性</h3>
            <ul>
                <li><strong>用户数据：</strong> 预置用户始终存在，新创建的用户会在刷新后消失</li>
                <li><strong>任务数据：</strong> 上传的任务记录会在刷新后清空</li>
                <li><strong>登录状态：</strong> Token存储在localStorage中，刷新后保持登录</li>
            </ul>
        </div>

        <div class="warning">
            <strong>🎯 测试建议：</strong>
            <ol>
                <li>按照上述顺序依次测试各个功能</li>
                <li>尝试各种边界情况和错误场景</li>
                <li>观察网络延迟模拟效果</li>
                <li>验证错误提示和成功消息</li>
                <li>测试用户体验和界面响应</li>
            </ol>
        </div>

        <div class="info">
            <strong>✅ 完成状态：</strong> 所有Mock功能已实现并可正常使用。系统可以完全独立运行，无需后台API支持。
        </div>
    </div>
</body>
</html>
