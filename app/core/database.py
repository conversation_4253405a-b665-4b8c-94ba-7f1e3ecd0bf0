from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData
from app.core.config import settings
from typing import AsyncGenerator
import logging

logger = logging.getLogger(__name__)

# 创建异步数据库引擎
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,  # 在调试模式下显示SQL语句
    pool_pre_ping=True,   # 连接池预检查
    pool_recycle=3600,    # 连接回收时间（1小时）
    # 明确指定 MySQL 方言以消除 IDE 警告
    connect_args={"charset": "utf8mb4"} if "mysql" in settings.database_url else {}
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 定义基础模型类
class Base(DeclarativeBase):
    """SQLAlchemy基础模型类"""
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def check_tables_exist():
    """检查数据库表是否存在"""
    try:
        from sqlalchemy import inspect
        async with engine.connect() as conn:
            # 使用 run_sync 来在异步连接中执行同步的 inspect 操作
            tables = await conn.run_sync(lambda sync_conn: inspect(sync_conn).get_table_names())
            return 'users' in tables
    except Exception as e:
        logger.error(f"检查表存在性失败: {e}")
        return False


async def init_db(force: bool = False):
    """初始化数据库

    Args:
        force: 是否强制重新创建表
    """
    try:
        # 导入所有模型以确保它们被注册
        from app.models.database import User, DataLoaderTask, ExternalMatchTask, DistributionRecord

        # 检查表是否已存在
        if not force and await check_tables_exist():
            logger.info("数据库表已存在，跳过初始化")
            return

        async with engine.begin() as conn:
            if force:
                # 强制模式：删除所有表后重新创建
                await conn.run_sync(Base.metadata.drop_all)
                logger.info("已删除所有现有表")

            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建成功")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_db():
    """关闭数据库连接"""
    await engine.dispose()
    logger.info("数据库连接已关闭")


async def check_db_connection():
    """检查数据库连接"""
    try:
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
            logger.info("数据库连接正常")
            return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False
