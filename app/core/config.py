from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 应用配置
    app_name: str = "MDM Backend API"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 数据库配置
    database_url: str = "sqlite:///./mdm_backend.db"
    auto_init_db: bool = True  # 是否自动初始化数据库表
    
    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: list = [".csv", ".xlsx", ".xls"]
    
    # CORS配置
    cors_origins: list = ["http://localhost:3000", "http://localhost:8080"]
    
    class Config:
        env_file = ".env"


settings = Settings()
