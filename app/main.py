from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
from contextlib import asynccontextmanager
import uvicorn
import os

from app.core.config import settings
from app.core.database import init_db, close_db, check_db_connection
from app.api import auth, users, data_loader, external_match


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动事件
    print("🚀 MDM Backend API is starting up...")

    # 检查数据库连接
    print("📊 检查数据库连接...")
    if await check_db_connection():
        print("✅ 数据库连接成功")

        # 根据配置决定是否初始化数据库表
        if settings.auto_init_db:
            print("🔧 检查并初始化数据库表...")
            await init_db()
            print("✅ 数据库初始化完成")
        else:
            print("⏭️ 跳过数据库自动初始化（配置已禁用）")
    else:
        print("❌ 数据库连接失败，请检查配置")

    # 创建上传目录
    os.makedirs(settings.upload_dir, exist_ok=True)
    os.makedirs(f"{settings.upload_dir}/data_loader", exist_ok=True)
    os.makedirs(f"{settings.upload_dir}/external_match", exist_ok=True)

    yield
    # 关闭事件
    print("🛑 MDM Backend API is shutting down...")
    print("📊 关闭数据库连接...")
    await close_db()
    print("✅ 数据库连接已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="MDM Backend API",
    description="""
    ## 主数据管理后台服务

    这是一个完整的MDM（主数据管理）后台API服务，提供以下功能：

    ### 🔐 认证功能
    - 用户登录/登出
    - JWT令牌认证
    - 用户权限管理

    ### 👥 用户管理
    - 用户CRUD操作
    - 角色管理（管理员/普通用户/查看者）
    - 用户信息维护

    ### 📁 数据加载器
    - 支持CSV/Excel文件上传
    - 实体类型选择（product/hco）
    - 实时处理进度跟踪
    - 处理结果统计

    ### 🔄 外部匹配
    - 文件上传和数据审核
    - 智能数据匹配
    - 多系统数据分发
    - 失败记录下载

    ### 📊 进度监控
    - 实时任务状态跟踪
    - 详细进度信息
    - 错误处理和报告

    ---

    **技术栈**: FastAPI + Python 3.10 + JWT + Pydantic

    **认证方式**: Bearer Token (JWT)

    **默认账号**:
    - 管理员: admin/admin
    - 普通用户: user/secret
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    contact={
        "name": "MDM开发团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "开发环境"
        },
        {
            "url": "https://api.mdm.com",
            "description": "生产环境"
        }
    ],
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(auth.router)
app.include_router(users.router)
app.include_router(data_loader.router)
app.include_router(external_match.router)

# 定义响应模型
class HelloResponse(BaseModel):
    message: str
    status: str
    data: Dict[str, Any] = {}

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

class SystemInfo(BaseModel):
    """系统信息模型"""
    app_name: str
    version: str
    description: str
    features: list

# 根路径 - 系统信息
@app.get("/", response_model=SystemInfo, summary="获取系统信息")
async def get_system_info():
    """
    获取MDM系统基本信息
    """
    return SystemInfo(
        app_name="MDM Backend API",
        version="1.0.0",
        description="主数据管理后台服务",
        features=[
            "用户管理和认证",
            "数据加载器",
            "外部数据匹配",
            "数据分发管理",
            "进度跟踪"
        ]
    )

# Hello 端点，支持自定义名称
@app.get("/hello/{name}", response_model=HelloResponse, summary="个性化问候")
async def say_hello(name: str):
    """
    个性化问候端点
    """
    return HelloResponse(
        message=f"你好 {name}！欢迎使用MDM系统",
        status="success",
        data={
            "name": name,
            "greeting": "你好",
            "system": "MDM Backend"
        }
    )

# 健康检查端点
@app.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """
    系统健康检查端点
    """
    return HealthResponse(
        status="healthy",
        service="MDM Backend",
        version="1.0.0"
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
