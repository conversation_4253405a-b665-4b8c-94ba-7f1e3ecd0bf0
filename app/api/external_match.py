import asyncio
import os
import uuid
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.config import settings
from app.core.database import get_db
from app.crud import external_match as external_match_crud
from app.models.external_match import (
    ExternalMatchTask, FileReview, MatchProgress, MatchResult,
    DistributionProgress, DistributionNode, MatchStatus, SystemNode, ProcessStatus
)
from app.models.user import User

router = APIRouter(prefix="/api/v1/external-match", tags=["外部匹配"])


@router.post("/upload", response_model=ExternalMatchTask, summary="上传文件创建外部匹配任务")
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文件并创建外部匹配任务

    - **file**: 上传的文件
    """
    # 验证文件类型
    if not file.filename.lower().endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件格式，请上传 CSV 或 Excel 文件"
        )

    # 验证文件大小
    content = await file.read()
    file_size = len(content)

    if file_size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件大小超过限制 ({settings.max_file_size / 1024 / 1024:.1f}MB)"
        )

    # 生成文件名和路径
    timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
    safe_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}_{file.filename}"
    file_path = os.path.join(settings.upload_dir, "external_match", safe_filename)

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存文件
    try:
        with open(file_path, "wb") as buffer:
            buffer.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件保存失败: {str(e)}"
        )

    # 创建数据库任务记录
    db_task = await external_match_crud.create_task(
        db=db,
        file_name=file.filename,
        file_path=file_path,
        created_by=current_user.id
    )

    # 转换为响应模型
    return ExternalMatchTask(
        id=db_task.id,
        batch_id=f"BATCH_{timestamp}_{db_task.id}",
        file_name=db_task.file_name,
        file_path=db_task.file_path,
        status=MatchStatus(db_task.status),  # 确保正确转换枚举
        progress=db_task.progress,
        total_records=db_task.total_records,
        matched_records=db_task.matched_records,
        unmatched_records=db_task.unmatched_records,
        error_message=db_task.error_message,
        result_file_path=db_task.result_file_path,
        result_data_id=f"MDM_RESULT_{timestamp}_{db_task.id}" if db_task.result_file_path else None,
        created_at=db_task.created_at,
        updated_at=db_task.updated_at,
        created_by=db_task.created_by
    )


@router.get("/tasks/{task_id}/review", response_model=FileReview, summary="获取文件审核信息")
async def get_file_review(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取文件审核信息
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 获取文件大小
    file_size = 0
    try:
        if os.path.exists(db_task.file_path):
            file_size = os.path.getsize(db_task.file_path)
    except Exception:
        pass

    # 模拟数据摘要（实际项目中应该分析文件内容）
    data_summary = {
        "总记录数": 5000,
        "列数": 15,
        "主要字段": ["公司名称", "地址", "联系人", "电话", "邮箱"],
        "数据质量": {
            "完整性": "85%",
            "准确性": "92%",
            "一致性": "88%"
        },
        "预估匹配率": "78%"
    }

    return FileReview(
        file_name=db_task.file_name,
        file_path=db_task.file_path,
        file_size=file_size,
        upload_time=db_task.created_at,
        data_summary=data_summary
    )


@router.post("/tasks/{task_id}/confirm", response_model=ExternalMatchTask, summary="确认文件并开始匹配")
async def confirm_and_start_matching(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    确认文件信息并开始匹配处理
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if db_task.status != MatchStatus.UPLOADED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务状态不正确，无法开始匹配"
        )

    # 更新任务状态和进度
    updated_task = await external_match_crud.update_task_status(
        db, task_id, MatchStatus.PROCESSING, progress=10
    )

    if not updated_task:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新任务状态失败"
        )

    print(f"🚀 开始匹配任务 {task_id}，状态: {updated_task.status}，进度: {updated_task.progress}")

    # 启动异步匹配处理
    asyncio.create_task(simulate_matching_process(task_id))

    # 转换为响应模型
    return ExternalMatchTask(
        id=updated_task.id,
        batch_id=f"BATCH_{updated_task.created_at.strftime('%Y%m%d_%H%M%S')}_{updated_task.id}",
        file_name=updated_task.file_name,
        file_path=updated_task.file_path,
        status=MatchStatus(updated_task.status),  # 确保正确转换枚举
        progress=updated_task.progress,
        total_records=updated_task.total_records,
        matched_records=updated_task.matched_records,
        unmatched_records=updated_task.unmatched_records,
        error_message=updated_task.error_message,
        result_file_path=updated_task.result_file_path,
        result_data_id=f"MDM_RESULT_{updated_task.created_at.strftime('%Y%m%d_%H%M%S')}_{updated_task.id}" if updated_task.result_file_path else None,
        created_at=updated_task.created_at,
        updated_at=updated_task.updated_at,
        created_by=updated_task.created_by
    )


@router.get("/tasks/{task_id}/progress", response_model=MatchProgress, summary="获取匹配进度")
async def get_match_progress(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取匹配处理进度
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 根据状态生成进度信息
    status_messages = {
        MatchStatus.UPLOADED: "文件已上传，等待确认",
        MatchStatus.REVIEWING: "文件审核中",
        MatchStatus.PROCESSING: "数据匹配处理中",
        MatchStatus.MATCHED: "匹配完成，准备结果",
        MatchStatus.DISTRIBUTING: "数据分发中",
        MatchStatus.COMPLETED: "处理完成",
        MatchStatus.FAILED: f"处理失败: {db_task.error_message or '未知错误'}"
    }

    current_steps = {
        MatchStatus.UPLOADED: "等待确认",
        MatchStatus.REVIEWING: "文件审核",
        MatchStatus.PROCESSING: "数据匹配",
        MatchStatus.MATCHED: "结果准备",
        MatchStatus.DISTRIBUTING: "数据分发",
        MatchStatus.COMPLETED: "完成",
        MatchStatus.FAILED: "失败"
    }

    batch_id = f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}"

    return MatchProgress(
        task_id=task_id,
        batch_id=batch_id,
        status=db_task.status,
        progress=db_task.progress,
        message=status_messages.get(db_task.status, "未知状态"),
        current_step=current_steps.get(db_task.status, "未知步骤"),
        estimated_time_remaining=60 if db_task.status == MatchStatus.PROCESSING else None
    )


@router.get("/tasks/{task_id}/result", response_model=MatchResult, summary="获取匹配结果")
async def get_match_result(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取匹配结果信息
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if db_task.status != MatchStatus.MATCHED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="匹配尚未完成"
        )

    batch_id = f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}"
    result_data_id = f"MDM_RESULT_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}" if db_task.result_file_path else None

    # 计算处理时间
    processing_time = 300  # 默认值
    if db_task.updated_at and db_task.created_at:
        processing_time = int((db_task.updated_at - db_task.created_at).total_seconds())

    return MatchResult(
        task_id=task_id,
        batch_id=batch_id,
        status=db_task.status,
        result_file_path=db_task.result_file_path,
        result_data_id=result_data_id,
        total_records=db_task.total_records or 0,
        matched_records=db_task.matched_records or 0,
        unmatched_records=db_task.unmatched_records or 0,
        match_rate=round((db_task.matched_records or 0) / (db_task.total_records or 1) * 100, 2),
        processing_time=processing_time,
        completed_at=db_task.updated_at or db_task.created_at
    )


@router.post("/tasks/{task_id}/distribute", response_model=ExternalMatchTask, summary="开始数据分发")
async def start_distribution(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    开始数据分发到各关联系统
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if db_task.status not in [MatchStatus.MATCHED, MatchStatus.DISTRIBUTING]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务状态不正确，当前状态: {db_task.status}，需要先完成匹配"
        )

    # 更新任务状态（如果还不是DISTRIBUTING状态）
    if db_task.status != MatchStatus.DISTRIBUTING:
        updated_task = await external_match_crud.update_task_status(
            db, task_id, MatchStatus.DISTRIBUTING, progress=0
        )

        if not updated_task:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新任务状态失败"
            )

        print(f"🚀 开始分发任务 {task_id}，状态: {updated_task.status}，进度: {updated_task.progress}")

        # 启动异步分发处理
        asyncio.create_task(simulate_distribution_process(task_id))
        db_task = updated_task
    else:
        print(f"⚠️ 任务 {task_id} 已在分发中，当前进度: {db_task.progress}%")

    # 转换为响应模型
    return ExternalMatchTask(
        id=db_task.id,
        batch_id=f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}",
        file_name=db_task.file_name,
        file_path=db_task.file_path,
        status=MatchStatus(db_task.status),  # 确保正确转换枚举
        progress=db_task.progress,
        total_records=db_task.total_records,
        matched_records=db_task.matched_records,
        unmatched_records=db_task.unmatched_records,
        error_message=db_task.error_message,
        result_file_path=db_task.result_file_path,
        result_data_id=f"MDM_RESULT_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}" if db_task.result_file_path else None,
        created_at=db_task.created_at,
        updated_at=db_task.updated_at,
        created_by=db_task.created_by
    )


@router.get("/tasks/{task_id}/distribution", response_model=DistributionProgress, summary="获取分发进度")
async def get_distribution_progress(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取数据分发进度
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 模拟分发节点状态 - 修复进度计算逻辑
    nodes = [
        DistributionNode(
            system=SystemNode.CRM,
            status=ProcessStatus.SUCCESS if db_task.progress >= 25 else (ProcessStatus.PROCESSING if db_task.status == MatchStatus.DISTRIBUTING else ProcessStatus.PENDING),
            progress=min(100, max(0, db_task.progress * 4)) if db_task.progress <= 25 else 100,
            message="CRM系统数据同步完成" if db_task.progress >= 25 else ("正在同步到CRM系统" if db_task.status == MatchStatus.DISTRIBUTING else "等待同步"),
            started_at=db_task.updated_at if db_task.status == MatchStatus.DISTRIBUTING else None,
            completed_at=db_task.updated_at if db_task.progress >= 25 else None
        ),
        DistributionNode(
            system=SystemNode.ERP,
            status=ProcessStatus.SUCCESS if db_task.progress >= 50 else (ProcessStatus.PROCESSING if db_task.progress >= 25 else ProcessStatus.PENDING),
            progress=min(100, max(0, (db_task.progress - 25) * 4)) if db_task.progress > 25 else 0,
            message="ERP系统数据同步完成" if db_task.progress >= 50 else ("正在同步到ERP系统" if db_task.progress >= 25 else "等待同步"),
            started_at=db_task.updated_at if db_task.progress >= 25 else None,
            completed_at=db_task.updated_at if db_task.progress >= 50 else None
        ),
        DistributionNode(
            system=SystemNode.DATA_WAREHOUSE,
            status=ProcessStatus.SUCCESS if db_task.progress >= 75 else (ProcessStatus.PROCESSING if db_task.progress >= 50 else ProcessStatus.PENDING),
            progress=min(100, max(0, (db_task.progress - 50) * 4)) if db_task.progress > 50 else 0,
            message="数据仓库同步完成" if db_task.progress >= 75 else ("正在同步到数据仓库" if db_task.progress >= 50 else "等待同步"),
            started_at=db_task.updated_at if db_task.progress >= 50 else None,
            completed_at=db_task.updated_at if db_task.progress >= 75 else None
        )
    ]

    batch_id = f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}"

    return DistributionProgress(
        task_id=task_id,
        batch_id=batch_id,
        overall_status=db_task.status,
        overall_progress=db_task.progress,
        nodes=nodes,
        estimated_time_remaining=30 if db_task.status == MatchStatus.DISTRIBUTING else None
    )


@router.get("/tasks/{task_id}/download-errors", summary="下载失败记录")
async def download_error_records(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    下载失败记录文件
    """
    # 从数据库获取任务
    db_task = await external_match_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if db_task.status != MatchStatus.FAILED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务未失败，无错误记录"
        )

    # 检查是否有失败文件路径
    if not db_task.failed_file_path or not os.path.exists(db_task.failed_file_path):
        # 如果没有失败文件，创建一个模拟的错误文件
        error_file_path = f"/tmp/error_records_{task_id}.csv"
        batch_id = f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}"
    else:
        error_file_path = db_task.failed_file_path
        batch_id = f"BATCH_{db_task.created_at.strftime('%Y%m%d_%H%M%S')}_{db_task.id}"

    return FileResponse(
        path=error_file_path,
        filename=f"error_records_{batch_id}.csv",
        media_type="text/csv"
    )


async def simulate_matching_process(task_id: int):
    """模拟匹配处理过程"""
    from app.core.database import AsyncSessionLocal

    async with AsyncSessionLocal() as db:
        try:
            # 获取任务信息
            db_task = await external_match_crud.get_task_by_id(db, task_id)
            if not db_task:
                print(f"❌ 任务 {task_id} 不存在")
                return

            print(f"🔄 开始处理任务 {task_id}")

            # 更新总记录数
            await external_match_crud.update_task_progress(
                db, task_id, progress=20, total_records=5000
            )

            # 模拟处理进度
            for i in range(40, 101, 20):  # 从40%开始，因为确认时已经设置为10%，更新总记录数时设置为20%
                await asyncio.sleep(2)  # 模拟处理时间

                processed_records = int(5000 * i / 100)
                matched_records = int(processed_records * 0.78)  # 78%匹配率
                unmatched_records = processed_records - matched_records

                await external_match_crud.update_task_progress(
                    db, task_id,
                    progress=i,
                    processed_records=processed_records,
                    matched_records=matched_records,
                    unmatched_records=unmatched_records
                )
                print(f"📊 任务 {task_id} 进度: {i}%")

            # 模拟匹配完成
            result_file_path = f"/results/match_result_{task_id}.csv"
            await external_match_crud.update_task_status(
                db, task_id, MatchStatus.MATCHED, progress=100
            )
            await external_match_crud.update_task_result_files(
                db, task_id, result_file_path=result_file_path
            )
            print(f"✅ 任务 {task_id} 匹配完成")

        except Exception as e:
            await external_match_crud.update_task_status(
                db, task_id, MatchStatus.FAILED, error_message=str(e)
            )
            print(f"❌ 任务 {task_id} 匹配失败: {e}")


async def simulate_distribution_process(task_id: int):
    """模拟分发处理过程"""
    from app.core.database import AsyncSessionLocal

    async with AsyncSessionLocal() as db:
        try:
            # 获取任务信息
            db_task = await external_match_crud.get_task_by_id(db, task_id)
            if not db_task:
                print(f"❌ Distribution: 任务 {task_id} 不存在")
                return

            print(f"🚀 开始分发任务 {task_id}")

            # 更新状态为分发中
            await external_match_crud.update_task_status(
                db, task_id, MatchStatus.DISTRIBUTING, progress=0
            )

            # 模拟分发过程
            for i in range(25, 101, 25):
                await asyncio.sleep(3)  # 模拟分发时间

                distributed_records = int((db_task.matched_records or 0) * i / 100)
                await external_match_crud.update_task_progress(
                    db, task_id,
                    progress=i,
                    distributed_records=distributed_records
                )
                print(f"📊 任务 {task_id} 分发进度: {i}%")

            # 模拟分发完成
            await external_match_crud.update_task_status(
                db, task_id, MatchStatus.COMPLETED, progress=100
            )
            print(f"✅ 任务 {task_id} 分发完成")

        except Exception as e:
            await external_match_crud.update_task_status(
                db, task_id, MatchStatus.FAILED, error_message=str(e)
            )
            print(f"❌ 任务 {task_id} 分发失败: {e}")
