from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.user import User, UserCreate, UserUpdate, UserRole
from app.core.security import get_password_hash
from app.core.database import get_db
from app.crud import user as user_crud
from app.api.deps import get_current_active_user, require_admin

router = APIRouter(prefix="/api/v1/users", tags=["用户管理"])


@router.get("/", response_model=List[User], summary="获取用户列表")
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户列表（仅管理员）

    - **skip**: 跳过的记录数
    - **limit**: 返回的记录数限制
    """
    db_users = await user_crud.get_users(db, skip=skip, limit=limit)

    # 转换为Pydantic模型
    users = []
    for db_user in db_users:
        user = User(
            id=db_user.id,
            username=db_user.username,
            email=db_user.email,
            full_name=db_user.full_name,
            role=db_user.role,
            is_active=db_user.is_active,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )
        users.append(user)

    return users


@router.post("/", response_model=User, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新用户（仅管理员）
    """
    # 检查用户名是否已存在
    existing_user = await user_crud.get_user_by_username(db, user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    if user_data.email:
        existing_email = await user_crud.get_user_by_email(db, str(user_data.email))
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    db_user = await user_crud.create_user(
        db=db,
        username=user_data.username,
        email=str(user_data.email) if user_data.email else None,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        role=user_data.role,
        is_active=user_data.is_active
    )

    # 转换为Pydantic模型
    return User(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        full_name=db_user.full_name,
        role=db_user.role,
        is_active=db_user.is_active,
        created_at=db_user.created_at,
        updated_at=db_user.updated_at
    )


@router.get("/{user_id}", response_model=User, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定用户的详情

    - 管理员可以查看所有用户
    - 普通用户只能查看自己的信息
    """
    # 从数据库查找用户
    db_user = await user_crud.get_user_by_id(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 权限检查
    if current_user.role != UserRole.ADMIN and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 转换为Pydantic模型
    return User(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        full_name=db_user.full_name,
        role=db_user.role,
        is_active=db_user.is_active,
        created_at=db_user.created_at,
        updated_at=db_user.updated_at
    )


@router.put("/{user_id}", response_model=User, summary="更新用户")
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新用户信息

    - 管理员可以更新所有用户
    - 普通用户只能更新自己的部分信息
    """
    # 从数据库查找用户
    db_user = await user_crud.get_user_by_id(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 权限检查
    if current_user.role != UserRole.ADMIN and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 更新用户信息
    update_data = user_update.model_dump(exclude_unset=True)

    # 转换EmailStr为str
    if "email" in update_data and update_data["email"] is not None:
        update_data["email"] = str(update_data["email"])

    # 普通用户不能修改角色和激活状态
    if current_user.role != UserRole.ADMIN:
        update_data.pop("role", None)
        update_data.pop("is_active", None)

    # 检查用户名是否重复
    if "username" in update_data and update_data["username"] != db_user.username:
        existing_user = await user_crud.get_user_by_username(db, update_data["username"])
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

    # 检查邮箱是否重复
    if "email" in update_data and update_data["email"] != db_user.email:
        existing_email = await user_crud.get_user_by_email(db, update_data["email"])
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

    # 处理密码更新
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))

    # 更新用户
    updated_user = await user_crud.update_user(db, user_id, **update_data)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )

    # 转换为Pydantic模型
    return User(
        id=updated_user.id,
        username=updated_user.username,
        email=updated_user.email,
        full_name=updated_user.full_name,
        role=updated_user.role,
        is_active=updated_user.is_active,
        created_at=updated_user.created_at,
        updated_at=updated_user.updated_at
    )


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    删除用户（仅管理员）
    """
    # 从数据库查找用户
    db_user = await user_crud.get_user_by_id(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )

    # 删除用户
    success = await user_crud.delete_user(db, user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )

    return {"message": "用户删除成功"}
