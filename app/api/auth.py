from datetime import timed<PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.config import settings
from app.core.security import create_access_token
from app.core.database import get_db
from app.crud import user as user_crud
from app.models.user import Token, LoginRequest, User

router = APIRouter(prefix="/api/v1/auth", tags=["认证"])


@router.post("/login", response_model=Token, summary="用户登录")
async def login(login_data: LoginRequest, db: AsyncSession = Depends(get_db)):
    """
    用户登录接口

    - **username**: 用户名
    - **password**: 密码
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"登录尝试 - 用户名: {login_data.username}")

    # 从数据库验证用户
    db_user = await user_crud.authenticate_user(db, login_data.username, login_data.password)
    if not db_user:
        logger.warning(f"用户验证失败: {login_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info(f"用户验证成功: {login_data.username}")

    if not db_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": db_user.username},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/logout", summary="用户登出")
async def logout(current_user: User = Depends(get_current_active_user)):
    """
    用户登出接口
    
    注意：由于使用JWT，实际的登出需要在客户端删除token
    """
    return {"message": "登出成功"}


@router.get("/me", response_model=User, summary="获取当前用户信息")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    获取当前登录用户的信息
    """
    return current_user


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(current_user: User = Depends(get_current_active_user)):
    """
    刷新访问令牌
    """
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": current_user.username}, 
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
