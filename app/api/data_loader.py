import asyncio
import os
from datetime import datetime, timezone
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.config import settings
from app.core.database import get_db
from app.crud import data_loader as data_loader_crud
from app.models.data_loader import (
    DataLoaderTask, DataLoaderProgress, DataLoaderResult,
    EntityType, ProcessStatus
)
from app.models.user import User

router = APIRouter(prefix="/api/v1/data-loader", tags=["数据加载器"])


@router.post("/upload", response_model=DataLoaderTask, summary="上传文件并创建数据加载任务")
async def upload_file(
    file: UploadFile = File(...),
    entity_type: EntityType = Form(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文件并创建数据加载任务

    - **file**: 上传的文件
    - **entity_type**: 实体类型（product 或 hco）
    """
    # 验证文件类型
    if not file.filename.lower().endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件格式，请上传 CSV 或 Excel 文件"
        )

    # 验证文件大小
    content = await file.read()
    file_size = len(content)

    if file_size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件大小超过限制 ({settings.max_file_size / 1024 / 1024:.1f}MB)"
        )

    # 生成文件名和路径
    timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
    safe_filename = f"{timestamp}_{file.filename}"
    file_path = os.path.join(settings.upload_dir, "data_loader", safe_filename)

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存文件
    try:
        with open(file_path, "wb") as buffer:
            buffer.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件保存失败: {str(e)}"
        )

    # 创建数据库任务记录
    db_task = await data_loader_crud.create_task(
        db=db,
        file_name=file.filename,
        file_path=file_path,
        entity_type=entity_type,
        created_by=current_user.id
    )

    # 启动异步处理
    asyncio.create_task(simulate_data_processing(db_task.id))

    # 转换为响应模型
    return DataLoaderTask(
        id=db_task.id,
        file_name=db_task.file_name,
        file_path=db_task.file_path,
        entity_type=db_task.entity_type,
        status=db_task.status,
        progress=db_task.progress,
        total_records=db_task.total_records,
        processed_records=db_task.processed_records,
        success_records=db_task.success_records,
        failed_records=db_task.failed_records,
        error_message=db_task.error_message,
        created_at=db_task.created_at,
        updated_at=db_task.updated_at,
        created_by=db_task.created_by
    )


@router.get("/tasks", response_model=List[DataLoaderTask], summary="获取数据加载任务列表")
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取数据加载任务列表
    """
    db_tasks = await data_loader_crud.get_tasks(db, skip=skip, limit=limit)

    # 转换为响应模型
    tasks = []
    for db_task in db_tasks:
        task = DataLoaderTask(
            id=db_task.id,
            file_name=db_task.file_name,
            file_path=db_task.file_path,
            entity_type=db_task.entity_type,
            status=db_task.status,
            progress=db_task.progress,
            total_records=db_task.total_records,
            processed_records=db_task.processed_records,
            success_records=db_task.success_records,
            failed_records=db_task.failed_records,
            error_message=db_task.error_message,
            created_at=db_task.created_at,
            updated_at=db_task.updated_at,
            created_by=db_task.created_by
        )
        tasks.append(task)

    return tasks


@router.get("/tasks/{task_id}", response_model=DataLoaderTask, summary="获取任务详情")
async def get_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定任务的详情
    """
    db_task = await data_loader_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    return DataLoaderTask(
        id=db_task.id,
        file_name=db_task.file_name,
        file_path=db_task.file_path,
        entity_type=db_task.entity_type,
        status=db_task.status,
        progress=db_task.progress,
        total_records=db_task.total_records,
        processed_records=db_task.processed_records,
        success_records=db_task.success_records,
        failed_records=db_task.failed_records,
        error_message=db_task.error_message,
        created_at=db_task.created_at,
        updated_at=db_task.updated_at,
        created_by=db_task.created_by
    )


@router.get("/tasks/{task_id}/progress", response_model=DataLoaderProgress, summary="获取任务进度")
async def get_task_progress(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务处理进度
    """
    db_task = await data_loader_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # 根据状态生成进度信息
    if db_task.status == ProcessStatus.PENDING:
        message = "任务等待处理中..."
    elif db_task.status == ProcessStatus.PROCESSING:
        message = f"正在处理数据... ({db_task.processed_records or 0}/{db_task.total_records or 0})"
    elif db_task.status == ProcessStatus.SUCCESS:
        message = "数据处理完成"
    elif db_task.status == ProcessStatus.FAILED:
        message = f"处理失败: {db_task.error_message or '未知错误'}"
    else:
        message = "未知状态"

    return DataLoaderProgress(
        task_id=task_id,
        status=db_task.status,
        progress=db_task.progress,
        message=message,
        total_records=db_task.total_records,
        processed_records=db_task.processed_records,
        success_records=db_task.success_records,
        failed_records=db_task.failed_records,
        estimated_time_remaining=30 if db_task.status == ProcessStatus.PROCESSING else None
    )


@router.get("/tasks/{task_id}/result", response_model=DataLoaderResult, summary="获取任务结果")
async def get_task_result(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务处理结果
    """
    db_task = await data_loader_crud.get_task_by_id(db, task_id)
    if not db_task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if db_task.status not in [ProcessStatus.SUCCESS, ProcessStatus.FAILED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="任务尚未完成"
        )

    return DataLoaderResult(
        task_id=task_id,
        status=db_task.status,
        total_records=db_task.total_records or 0,
        success_records=db_task.success_records or 0,
        failed_records=db_task.failed_records or 0,
        error_details={"error_message": db_task.error_message} if db_task.error_message else None,
        completed_at=db_task.updated_at or db_task.created_at
    )


async def simulate_data_processing(task_id: int):
    """模拟数据处理过程"""
    from app.core.database import AsyncSessionLocal

    async with AsyncSessionLocal() as db:
        try:
            # 获取任务信息
            db_task = await data_loader_crud.get_task_by_id(db, task_id)
            if not db_task:
                print(f"❌ 任务 {task_id} 不存在")
                return

            print(f"🔄 开始处理数据加载任务 {task_id}")

            # 更新状态为处理中，并设置总记录数
            await data_loader_crud.update_task_status(
                db, task_id, ProcessStatus.PROCESSING, progress=0
            )
            await data_loader_crud.update_task_progress(
                db, task_id, progress=10, total_records=1000
            )

            # 模拟处理进度
            for i in range(20, 101, 10):
                await asyncio.sleep(1)  # 模拟处理时间

                processed_records = int(1000 * i / 100)
                success_records = int(processed_records * 0.95)  # 95%成功率
                failed_records = processed_records - success_records

                await data_loader_crud.update_task_progress(
                    db, task_id,
                    progress=i,
                    processed_records=processed_records,
                    success_records=success_records,
                    failed_records=failed_records
                )
                print(f"📊 数据加载任务 {task_id} 进度: {i}%")

            # 模拟处理完成
            await data_loader_crud.update_task_status(
                db, task_id, ProcessStatus.SUCCESS, progress=100
            )
            print(f"✅ 数据加载任务 {task_id} 处理完成")

        except Exception as e:
            await data_loader_crud.update_task_status(
                db, task_id, ProcessStatus.FAILED, error_message=str(e)
            )
            print(f"❌ 数据加载任务 {task_id} 处理失败: {e}")
