"""
数据加载器任务 CRUD 操作
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
from app.models.database import DataLoaderTask as DBDataLoaderTask, EntityType, ProcessStatus
from datetime import datetime, timezone


async def get_task_by_id(db: AsyncSession, task_id: int) -> Optional[DBDataLoaderTask]:
    """根据ID获取数据加载器任务"""
    result = await db.execute(select(DBDataLoaderTask).where(DBDataLoaderTask.id == task_id))
    return result.scalar_one_or_none()


async def get_tasks(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100,
    user_id: Optional[int] = None
) -> List[DBDataLoaderTask]:
    """获取数据加载器任务列表"""
    query = select(DBDataLoaderTask).order_by(desc(DBDataLoaderTask.created_at))
    
    # 如果指定了用户ID，只返回该用户的任务
    if user_id is not None:
        query = query.where(DBDataLoaderTask.created_by == user_id)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_tasks_count(db: AsyncSession, user_id: Optional[int] = None) -> int:
    """获取任务总数"""
    query = select(func.count(DBDataLoaderTask.id))
    
    if user_id is not None:
        query = query.where(DBDataLoaderTask.created_by == user_id)
    
    result = await db.execute(query)
    return result.scalar()


async def create_task(
    db: AsyncSession,
    file_name: str,
    file_path: str,
    entity_type: EntityType,
    created_by: int
) -> DBDataLoaderTask:
    """创建数据加载器任务"""
    db_task = DBDataLoaderTask(
        file_name=file_name,
        file_path=file_path,
        entity_type=entity_type,
        status=ProcessStatus.PENDING,
        progress=0,
        created_by=created_by,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(db_task)
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def update_task_status(
    db: AsyncSession,
    task_id: int,
    status: ProcessStatus,
    progress: Optional[int] = None,
    error_message: Optional[str] = None
) -> Optional[DBDataLoaderTask]:
    """更新任务状态"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return None
    
    db_task.status = status
    if progress is not None:
        db_task.progress = progress
    if error_message is not None:
        db_task.error_message = error_message
    
    db_task.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def update_task_progress(
    db: AsyncSession,
    task_id: int,
    progress: Optional[int] = None,
    total_records: Optional[int] = None,
    processed_records: Optional[int] = None,
    success_records: Optional[int] = None,
    failed_records: Optional[int] = None
) -> Optional[DBDataLoaderTask]:
    """更新任务进度"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return None
    
    if progress is not None:
        db_task.progress = progress
    if total_records is not None:
        db_task.total_records = total_records
    if processed_records is not None:
        db_task.processed_records = processed_records
    if success_records is not None:
        db_task.success_records = success_records
    if failed_records is not None:
        db_task.failed_records = failed_records
    
    db_task.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def delete_task(db: AsyncSession, task_id: int) -> bool:
    """删除任务"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return False
    
    await db.delete(db_task)
    await db.commit()
    return True


async def get_tasks_by_status(
    db: AsyncSession, 
    status: ProcessStatus,
    limit: int = 100
) -> List[DBDataLoaderTask]:
    """根据状态获取任务列表"""
    query = select(DBDataLoaderTask).where(
        DBDataLoaderTask.status == status
    ).order_by(desc(DBDataLoaderTask.created_at)).limit(limit)
    
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_tasks_by_entity_type(
    db: AsyncSession,
    entity_type: EntityType,
    limit: int = 100
) -> List[DBDataLoaderTask]:
    """根据实体类型获取任务列表"""
    query = select(DBDataLoaderTask).where(
        DBDataLoaderTask.entity_type == entity_type
    ).order_by(desc(DBDataLoaderTask.created_at)).limit(limit)
    
    result = await db.execute(query)
    return list(result.scalars().all())
