"""
用户 CRUD 操作
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from app.models.database import User as DBUser
from app.models.user import UserRole
from app.core.security import verify_password
from datetime import datetime, timezone


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[DBUser]:
    """根据ID获取用户"""
    result = await db.execute(select(DBUser).where(DBUser.id == user_id))
    return result.scalar_one_or_none()


async def get_user_by_username(db: AsyncSession, username: str) -> Optional[DBUser]:
    """根据用户名获取用户"""
    result = await db.execute(select(DBUser).where(DBUser.username == username))
    return result.scalar_one_or_none()


async def get_user_by_email(db: AsyncSession, email: str) -> Optional[DBUser]:
    """根据邮箱获取用户"""
    result = await db.execute(select(DBUser).where(DBUser.email == email))
    return result.scalar_one_or_none()


async def get_users(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100,
    is_active: Optional[bool] = None
) -> List[DBUser]:
    """获取用户列表"""
    query = select(DBUser)
    
    if is_active is not None:
        query = query.where(DBUser.is_active == is_active)
    
    query = query.offset(skip).limit(limit).order_by(DBUser.created_at.desc())
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_users_count(db: AsyncSession, is_active: Optional[bool] = None) -> int:
    """获取用户总数"""
    query = select(func.count(DBUser.id))
    
    if is_active is not None:
        query = query.where(DBUser.is_active == is_active)
    
    result = await db.execute(query)
    return result.scalar()


async def create_user(
    db: AsyncSession,
    username: str,
    email: str,
    full_name: str,
    hashed_password: str,
    role: UserRole = UserRole.USER,
    is_active: bool = True
) -> DBUser:
    """创建用户"""
    db_user = DBUser(
        username=username,
        email=email,
        full_name=full_name,
        hashed_password=hashed_password,
        role=role,
        is_active=is_active,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def update_user(
    db: AsyncSession,
    user_id: int,
    **kwargs
) -> Optional[DBUser]:
    """更新用户"""
    db_user = await get_user_by_id(db, user_id)
    if not db_user:
        return None
    
    for key, value in kwargs.items():
        if hasattr(db_user, key) and value is not None:
            setattr(db_user, key, value)
    
    db_user.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def delete_user(db: AsyncSession, user_id: int) -> bool:
    """删除用户"""
    db_user = await get_user_by_id(db, user_id)
    if not db_user:
        return False
    
    await db.delete(db_user)
    await db.commit()
    return True


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[DBUser]:
    """验证用户"""
    user = await get_user_by_username(db, username)
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user


async def get_users_by_role(
    db: AsyncSession, 
    role: UserRole,
    limit: int = 100
) -> List[DBUser]:
    """根据角色获取用户列表"""
    query = select(DBUser).where(DBUser.role == role).limit(limit)
    result = await db.execute(query)
    return list(result.scalars().all())
