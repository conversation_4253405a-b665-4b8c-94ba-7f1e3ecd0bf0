"""
外部匹配任务 CRUD 操作
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
from app.models.database import ExternalMatchTask as DBExternalMatchTask
from app.models.external_match import MatchStatus
from datetime import datetime, timezone


async def get_task_by_id(db: AsyncSession, task_id: int) -> Optional[DBExternalMatchTask]:
    """根据ID获取外部匹配任务"""
    result = await db.execute(select(DBExternalMatchTask).where(DBExternalMatchTask.id == task_id))
    return result.scalar_one_or_none()


async def get_tasks(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100,
    user_id: Optional[int] = None
) -> List[DBExternalMatchTask]:
    """获取外部匹配任务列表"""
    query = select(DBExternalMatchTask).order_by(desc(DBExternalMatchTask.created_at))
    
    # 如果指定了用户ID，只返回该用户的任务
    if user_id is not None:
        query = query.where(DBExternalMatchTask.created_by == user_id)
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_tasks_count(db: AsyncSession, user_id: Optional[int] = None) -> int:
    """获取任务总数"""
    query = select(func.count(DBExternalMatchTask.id))
    
    if user_id is not None:
        query = query.where(DBExternalMatchTask.created_by == user_id)
    
    result = await db.execute(query)
    return result.scalar()


async def create_task(
    db: AsyncSession,
    file_name: str,
    file_path: str,
    created_by: int
) -> DBExternalMatchTask:
    """创建外部匹配任务"""
    db_task = DBExternalMatchTask(
        file_name=file_name,
        file_path=file_path,
        status=MatchStatus.UPLOADED,
        progress=0,
        created_by=created_by,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(db_task)
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def update_task_status(
    db: AsyncSession,
    task_id: int,
    status: MatchStatus,
    progress: Optional[int] = None,
    error_message: Optional[str] = None
) -> Optional[DBExternalMatchTask]:
    """更新任务状态"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return None
    
    db_task.status = status
    if progress is not None:
        db_task.progress = progress
    if error_message is not None:
        db_task.error_message = error_message
    
    db_task.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def update_task_progress(
    db: AsyncSession,
    task_id: int,
    progress: Optional[int] = None,
    total_records: Optional[int] = None,
    processed_records: Optional[int] = None,
    matched_records: Optional[int] = None,
    unmatched_records: Optional[int] = None,
    distributed_records: Optional[int] = None,
    failed_records: Optional[int] = None
) -> Optional[DBExternalMatchTask]:
    """更新任务进度"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return None
    
    if progress is not None:
        db_task.progress = progress
    if total_records is not None:
        db_task.total_records = total_records
    if processed_records is not None:
        db_task.processed_records = processed_records
    if matched_records is not None:
        db_task.matched_records = matched_records
    if unmatched_records is not None:
        db_task.unmatched_records = unmatched_records
    if distributed_records is not None:
        db_task.distributed_records = distributed_records
    if failed_records is not None:
        db_task.failed_records = failed_records
    
    db_task.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def update_task_result_files(
    db: AsyncSession,
    task_id: int,
    result_file_path: Optional[str] = None,
    failed_file_path: Optional[str] = None
) -> Optional[DBExternalMatchTask]:
    """更新任务结果文件路径"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return None
    
    if result_file_path is not None:
        db_task.result_file_path = result_file_path
    
    if failed_file_path is not None:
        db_task.failed_file_path = failed_file_path
    
    db_task.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_task)
    return db_task


async def delete_task(db: AsyncSession, task_id: int) -> bool:
    """删除任务"""
    db_task = await get_task_by_id(db, task_id)
    if not db_task:
        return False
    
    await db.delete(db_task)
    await db.commit()
    return True


async def get_tasks_by_status(
    db: AsyncSession,
    status: MatchStatus,
    limit: int = 100
) -> List[DBExternalMatchTask]:
    """根据状态获取任务列表"""
    query = select(DBExternalMatchTask).where(
        DBExternalMatchTask.status == status
    ).order_by(desc(DBExternalMatchTask.created_at)).limit(limit)
    
    result = await db.execute(query)
    return list(result.scalars().all())
