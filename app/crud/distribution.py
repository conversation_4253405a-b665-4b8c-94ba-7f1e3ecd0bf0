"""
分发记录 CRUD 操作
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
from app.models.database import DistributionRecord as DBDistributionRecord, ProcessStatus
from datetime import datetime, timezone


async def get_record_by_id(db: AsyncSession, record_id: int) -> Optional[DBDistributionRecord]:
    """根据ID获取分发记录"""
    result = await db.execute(select(DBDistributionRecord).where(DBDistributionRecord.id == record_id))
    return result.scalar_one_or_none()


async def get_records_by_task_id(
    db: AsyncSession, 
    task_id: int
) -> List[DBDistributionRecord]:
    """根据任务ID获取分发记录列表"""
    query = select(DBDistributionRecord).where(
        DBDistributionRecord.task_id == task_id
    ).order_by(desc(DBDistributionRecord.created_at))
    
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_records(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100
) -> List[DBDistributionRecord]:
    """获取分发记录列表"""
    query = select(DBDistributionRecord).order_by(desc(DBDistributionRecord.created_at))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_records_count(db: AsyncSession) -> int:
    """获取分发记录总数"""
    result = await db.execute(select(func.count(DBDistributionRecord.id)))
    return result.scalar()


async def create_record(
    db: AsyncSession,
    task_id: int,
    target_system: str,
    record_data: dict,
    created_by: int
) -> DBDistributionRecord:
    """创建分发记录"""
    db_record = DBDistributionRecord(
        task_id=task_id,
        target_system=target_system,
        record_data=str(record_data),  # 转换为字符串存储
        status=ProcessStatus.PENDING,
        created_by=created_by,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(db_record)
    await db.commit()
    await db.refresh(db_record)
    return db_record


async def update_record_status(
    db: AsyncSession,
    record_id: int,
    status: ProcessStatus,
    error_message: Optional[str] = None,
    response_data: Optional[str] = None
) -> Optional[DBDistributionRecord]:
    """更新分发记录状态"""
    db_record = await get_record_by_id(db, record_id)
    if not db_record:
        return None
    
    db_record.status = status
    if error_message is not None:
        db_record.error_message = error_message
    if response_data is not None:
        db_record.response_data = response_data
    
    db_record.updated_at = datetime.now(timezone.utc)
    
    await db.commit()
    await db.refresh(db_record)
    return db_record


async def batch_create_records(
    db: AsyncSession,
    task_id: int,
    target_system: str,
    records_data: List[dict],
    created_by: int
) -> List[DBDistributionRecord]:
    """批量创建分发记录"""
    db_records = []
    
    for record_data in records_data:
        db_record = DBDistributionRecord(
            task_id=task_id,
            target_system=target_system,
            record_data=str(record_data),
            status=ProcessStatus.PENDING,
            created_by=created_by,
            created_at=datetime.now(timezone.utc)
        )
        db_records.append(db_record)
        db.add(db_record)
    
    await db.commit()
    
    # 刷新所有记录
    for db_record in db_records:
        await db.refresh(db_record)
    
    return db_records


async def delete_records_by_task_id(db: AsyncSession, task_id: int) -> int:
    """删除指定任务的所有分发记录"""
    records = await get_records_by_task_id(db, task_id)
    count = len(records)
    
    for record in records:
        await db.delete(record)
    
    await db.commit()
    return count


async def get_records_by_status(
    db: AsyncSession, 
    status: ProcessStatus,
    limit: int = 100
) -> List[DBDistributionRecord]:
    """根据状态获取分发记录列表"""
    query = select(DBDistributionRecord).where(
        DBDistributionRecord.status == status
    ).order_by(desc(DBDistributionRecord.created_at)).limit(limit)
    
    result = await db.execute(query)
    return list(result.scalars().all())


async def get_records_by_system(
    db: AsyncSession,
    target_system: str,
    limit: int = 100
) -> List[DBDistributionRecord]:
    """根据目标系统获取分发记录列表"""
    query = select(DBDistributionRecord).where(
        DBDistributionRecord.target_system == target_system
    ).order_by(desc(DBDistributionRecord.created_at)).limit(limit)
    
    result = await db.execute(query)
    return list(result.scalars().all())
