from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class MatchStatus(str, Enum):
    """匹配状态枚举"""
    UPLOADED = "UPLOADED"
    REVIEWING = "REVIEWING"
    PROCESSING = "PROCESSING"
    MATCHED = "MATCHED"
    DISTRIBUTING = "DISTRIBUTING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class ProcessStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"


class SystemNode(str, Enum):
    """系统节点枚举"""
    CRM = "crm"
    ERP = "erp"
    MDM = "mdm"
    DATA_WAREHOUSE = "data_warehouse"


class ExternalMatchUpload(BaseModel):
    """外部匹配上传请求"""
    file_name: str
    file_size: int


class FileReview(BaseModel):
    """文件审核信息"""
    file_name: str
    file_path: str
    file_size: int
    upload_time: datetime
    data_summary: Dict[str, Any]  # 数据摘要信息


class ExternalMatchTask(BaseModel):
    """外部匹配任务"""
    id: int
    batch_id: str
    file_name: str
    file_path: str
    status: MatchStatus
    progress: int = 0
    total_records: Optional[int] = None
    matched_records: Optional[int] = None
    unmatched_records: Optional[int] = None
    error_message: Optional[str] = None
    result_file_path: Optional[str] = None
    result_data_id: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: int

    class Config:
        from_attributes = True


class MatchProgress(BaseModel):
    """匹配进度"""
    task_id: int
    batch_id: str
    status: MatchStatus
    progress: int
    message: str
    current_step: str
    estimated_time_remaining: Optional[int] = None


class MatchResult(BaseModel):
    """匹配结果"""
    task_id: int
    batch_id: str
    status: MatchStatus
    result_file_path: Optional[str] = None
    result_data_id: Optional[str] = None
    total_records: int
    matched_records: int
    unmatched_records: int
    match_rate: float
    processing_time: int  # 处理时间（秒）
    completed_at: datetime


class DistributionNode(BaseModel):
    """分发节点"""
    system: SystemNode
    status: ProcessStatus
    progress: int
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class DistributionProgress(BaseModel):
    """分发进度"""
    task_id: int
    batch_id: str
    overall_status: MatchStatus
    overall_progress: int
    nodes: List[DistributionNode]
    estimated_time_remaining: Optional[int] = None



