from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class EntityType(str, Enum):
    """实体类型枚举"""
    PRODUCT = "product"
    HCO = "hco"


class ProcessStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"


class DataLoaderUpload(BaseModel):
    """数据加载器上传请求"""
    entity_type: EntityType
    file_name: str
    file_size: int


class DataLoaderTask(BaseModel):
    """数据加载器任务"""
    id: int
    file_name: str
    file_path: str
    entity_type: EntityType
    status: ProcessStatus
    progress: int = 0
    total_records: Optional[int] = None
    processed_records: Optional[int] = None
    success_records: Optional[int] = None
    failed_records: Optional[int] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: int

    class Config:
        from_attributes = True


class DataLoaderProgress(BaseModel):
    """数据加载器进度"""
    task_id: int
    status: ProcessStatus
    progress: int
    message: str
    total_records: Optional[int] = None
    processed_records: Optional[int] = None
    success_records: Optional[int] = None
    failed_records: Optional[int] = None
    estimated_time_remaining: Optional[int] = None  # 秒


class DataLoaderResult(BaseModel):
    """数据加载器结果"""
    task_id: int
    status: ProcessStatus
    total_records: int
    success_records: int
    failed_records: int
    error_details: Optional[Dict[str, Any]] = None
    completed_at: datetime
