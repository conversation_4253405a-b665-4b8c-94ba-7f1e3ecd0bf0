from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum
from sqlalchemy.sql import func
from app.core.database import Base
from app.models.user import UserRole
from app.models.data_loader import EntityType, ProcessStatus
from app.models.external_match import MatchStatus


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255), nullable=False)
    role = Column(SQLEnum(UserRole), default=UserRole.USER, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class DataLoaderTask(Base):
    """数据加载器任务表"""
    __tablename__ = "data_loader_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    entity_type = Column(SQLEnum(EntityType), nullable=False)
    status = Column(SQLEnum(ProcessStatus), default=ProcessStatus.PENDING, nullable=False)
    progress = Column(Integer, default=0)
    total_records = Column(Integer)
    processed_records = Column(Integer, default=0)
    success_records = Column(Integer, default=0)
    failed_records = Column(Integer, default=0)
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, nullable=False)


class ExternalMatchTask(Base):
    """外部匹配任务表"""
    __tablename__ = "external_match_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    status = Column(SQLEnum(MatchStatus, native_enum=False), default=MatchStatus.UPLOADED, nullable=False)
    progress = Column(Integer, default=0)
    total_records = Column(Integer)
    processed_records = Column(Integer, default=0)
    matched_records = Column(Integer, default=0)
    unmatched_records = Column(Integer, default=0)
    distributed_records = Column(Integer, default=0)
    failed_records = Column(Integer, default=0)
    error_message = Column(Text)
    result_file_path = Column(String(500))
    failed_file_path = Column(String(500))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, nullable=False)


class DistributionRecord(Base):
    """分发记录表"""
    __tablename__ = "distribution_records"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, nullable=False)  # 关联的外部匹配任务ID
    target_system = Column(String(100), nullable=False)  # 目标系统名称
    record_data = Column(Text)  # 分发的记录数据
    status = Column(SQLEnum(ProcessStatus), default=ProcessStatus.PENDING, nullable=False)
    error_message = Column(Text)
    response_data = Column(Text)  # 响应数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, nullable=False)
