# MDM Backend

Master Data Management Backend Service built with FastAPI and Python 3.10.

## Features

- **用户管理系统** - 完整的用户CRUD操作，支持角色管理
- **认证授权** - JWT令牌认证，支持登录/登出
- **数据加载器** - 支持CSV/Excel文件上传，实体类型选择（product/hco）
- **外部匹配** - 文件审核、数据匹配、结果分发到多个系统
- **进度跟踪** - 实时任务进度监控和状态更新
- **文件管理** - 安全的文件上传和下载功能
- **API文档** - 自动生成的Swagger UI文档
- **中文界面** - 所有接口和响应均支持中文

## Requirements

- Python 3.10+
- FastAPI - 高性能Web框架
- Uvicorn - ASGI服务器
- SQLAlchemy - ORM（未来数据库支持）
- JWT - 认证令牌
- Pydantic - 数据验证

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mdm-backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up the database:
```bash
# Copy environment configuration
cp .env.example .env

# Complete database setup (recommended for first-time setup)
python manage_db.py setup

# Or step by step:
# python manage_db.py create-db      # Create MySQL database
# python manage_db.py init           # Initialize tables
# python manage_db.py create-defaults # Create default users
```

## Database Management

The project includes a comprehensive database management tool `manage_db.py`:

```bash
# Complete setup (creates database + tables + default users)
python manage_db.py setup

# Individual operations
python manage_db.py create-db          # Create MySQL database
python manage_db.py init               # Initialize table structure
python manage_db.py check              # Check database status
python manage_db.py test               # Test database connection
python manage_db.py create-admin       # Create admin user (interactive)
python manage_db.py create-defaults    # Create default users (admin/admin, user/secret)
```

## Running the Application

### Method 1: Using the main script
```bash
python main.py
```

### Method 2: Using uvicorn directly
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The application will be available at:
- API: http://localhost:8000
- Interactive API docs (Swagger UI): http://localhost:8000/docs
- Alternative API docs (ReDoc): http://localhost:8000/redoc

## API Endpoints

### 系统信息
- `GET /` - 获取系统基本信息
- `GET /hello/{name}` - 个性化问候
- `GET /health` - 系统健康检查

### 用户认证 (`/api/v1/auth/`)
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `GET /me` - 获取当前用户信息
- `POST /refresh` - 刷新访问令牌

### 用户管理 (`/api/v1/users/`)
- `GET /` - 获取用户列表（管理员）
- `POST /` - 创建用户（管理员）
- `GET /{user_id}` - 获取用户详情
- `PUT /{user_id}` - 更新用户信息
- `DELETE /{user_id}` - 删除用户（管理员）

### 数据加载器 (`/api/v1/data-loader/`)
- `POST /upload` - 上传文件并创建数据加载任务
- `GET /tasks` - 获取数据加载任务列表
- `GET /tasks/{task_id}` - 获取任务详情
- `GET /tasks/{task_id}/progress` - 获取任务进度
- `GET /tasks/{task_id}/result` - 获取任务结果

### 外部匹配 (`/api/v1/external-match/`)
- `POST /upload` - 上传文件创建外部匹配任务
- `GET /tasks/{task_id}/review` - 获取文件审核信息
- `POST /tasks/{task_id}/confirm` - 确认文件并开始匹配
- `GET /tasks/{task_id}/progress` - 获取匹配进度
- `GET /tasks/{task_id}/result` - 获取匹配结果
- `POST /tasks/{task_id}/distribute` - 开始数据分发
- `GET /tasks/{task_id}/distribution` - 获取分发进度
- `GET /tasks/{task_id}/download-errors` - 下载失败记录

### 示例响应

**登录成功**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

**系统信息**
```json
{
  "app_name": "MDM Backend API",
  "version": "1.0.0",
  "description": "主数据管理后台服务",
  "features": [
    "用户管理和认证",
    "数据加载器",
    "外部数据匹配",
    "数据分发管理",
    "进度跟踪"
  ]
}
```

## Development

The application is configured with auto-reload enabled for development. Any changes to the code will automatically restart the server.

## Project Structure

```
mdm-backend/
├── app/
│   ├── __init__.py
│   └── main.py          # FastAPI application
├── main.py              # Application entry point
├── requirements.txt     # Python dependencies
├── .gitignore          # Git ignore file
└── README.md           # This file
```

## Next Steps

This is a basic Hello World application. You can extend it by:

1. Adding more API endpoints
2. Implementing database connectivity
3. Adding authentication and authorization
4. Implementing business logic for Master Data Management
5. Adding tests
6. Setting up CI/CD pipelines

## License

This project is licensed under the MIT License.
